﻿/* General Body Styling */
body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    background-color: #f8f9fa;
    padding-top: 50px; /* <PERSON><PERSON><PERSON> bảo không gian cho navbar */
}

/* Sidebar Styling */
.sidebar {
    position: fixed;
    left: -250px; /* Mặc định ẩn sidebar */
    top: 0;
    width: 250px;
    height: 100%;
    background: #343a40;
    color: white;
    padding: 20px;
    padding-top: 80px; /* Đ<PERSON>y nội dung xuống dưới, tránh đè lên nút menu */
    transition: left 0.5s cubic-bezier(0.25, 0.8, 0.25, 1); /* Hiệu ứng mượt */
    z-index: 1000;
    /* --- THÊM PHẦN NÀY ĐỂ CUỘN --- */
    overflow-y: auto; /* Cho phép cuộn dọc */
    max-height: 100vh; /* Chiều cao tối đa bằng viewport */
    scrollbar-width: thin; /* Firefox: thanh cuộn mảnh */
}
    /* Thanh cuộn đẹp cho Chrome */
    .sidebar::-webkit-scrollbar {
        width: 6px;
    }

    .sidebar::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 10px;
    }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }

    /* Khi sidebar mở */
    .sidebar.active {
        left: 0;
    }

/* Main Content Styling */
.main-content {
    margin-left: 0; /* Không gian mặc định khi sidebar ẩn */
    transition: margin-left 0.5s cubic-bezier(0.25, 0.8, 0.25, 1); /* Hiệu ứng mượt */
    padding-top: 60px; /* Khoảng cách từ navbar */
    z-index: 500;
}

/* Khi sidebar mở */
.sidebar.active + .main-content {
    margin-left: 250px; /* Nội dung chính dịch chuyển */
}

/* Menu Toggle Button */
.menu-toggle {
    position: fixed; /* Nút menu cố định trên màn hình */
    top: 20px; /* Khoảng cách từ trên xuống */
    left: 20px; /* Khoảng cách từ trái */
    z-index: 1100; /* Hiển thị trên sidebar */
    font-size: 1.8rem; /* Kích thước biểu tượng */
    background: none; /* Loại bỏ màu nền */
    border: none; /* Loại bỏ viền */
    color: #343a40; /* Màu biểu tượng */
    cursor: pointer; /* Thay đổi con trỏ thành dạng tay khi rê chuột */
    transition: left 0.5s cubic-bezier(0.25, 0.8, 0.25, 1); /* Hiệu ứng mượt */
}

    .menu-toggle ion-icon {
        font-size: 1.8rem;
    }

/* Sidebar Nav Link Styling */
.sidebar .nav-link {
    color: #ffffff;
    padding: 10px 20px;
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: background 0.3s ease; /* Hiệu ứng hover mượt */
}

    .sidebar .nav-link:hover {
        background-color: #6c757d;
        border-radius: 5px; /* Góc bo tròn */
    }

    /* Sidebar Icons */
    .sidebar .nav-link ion-icon {
        font-size: 1.5rem;
        margin-right: 10px; /* Khoảng cách giữa icon và text */
    }

/* Logo Styling */
/* Logo Styling */
.navbar-brand .nav-logo {
    height: 120px; /* Chiều cao lớn hơn cho logo */
    max-height: 150px; /* Đảm bảo logo không quá lớn */
    width: auto; /* Giữ tỷ lệ của logo */
    object-fit: contain; /* Đảm bảo logo không bị méo */
    display: block; /* Giúp logo luôn là một khối riêng */
    margin: 0 auto; /* Căn giữa logo */
    border-radius: 15px; /* Làm mềm các góc cạnh nếu logo hình vuông */
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2); /* Thêm bóng mờ để logo nổi bật */
    transition: transform 0.3s ease-in-out; /* Hiệu ứng mượt khi hover */
}
    /* Hiệu ứng khi hover vào logo */
    .navbar-brand .nav-logo:hover {
        transform: scale(1.1); /* Phóng to nhẹ logo */
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3); /* Tăng bóng mờ khi hover */
    }


/* Collapsed Sidebar */
.sidebar.collapsed {
    width: 80px; /* Thu gọn sidebar */
    left: 0; /* Giữ sidebar mở */
}

    .sidebar.collapsed .nav-link {
        text-align: center; /* Canh giữa icon */
    }

        .sidebar.collapsed .nav-link span {
            display: none; /* Ẩn văn bản khi thu gọn */
        }

        .sidebar.collapsed .nav-link ion-icon {
            margin-right: 0; /* Loại bỏ khoảng cách icon */
        }

/* Navbar Styling */
.navbar {
    position: relative;
    z-index: 900; /* Đảm bảo navbar luôn ở phía trên nội dung */
}

/* Footer Styling */
footer {
    text-align: center;
    padding: 15px;
    background-color: #f8f9fa;
    color: #6c757d;
    border-top: 1px solid #dee2e6;
}
/*icon giỏ hàng ở dưới cùng bên phải*/
.cart-icon {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #ff6600;
    color: white;
    padding: 15px;
    border-radius: 50%;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    font-size: 24px;
}

    .cart-icon:hover {
        background: #e65c00;
    }

.cart-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: red;
    color: white;
    font-size: 14px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}
/*icon acc */
/* Đảm bảo rằng phần tử ở góc phải */
.account-icon-container {
    position: fixed; /* Để cố định vị trí */
    top: 10px; /* Cách từ trên cùng */
    right: 10px; /* Cách từ bên phải */
    z-index: 1000; /* Đảm bảo nó nằm trên các phần tử khác */
}

/* Kiểu cho phần tử account-icon */
.account-icon {
    display: flex;
    align-items: center;
    gap: 10px; /* Khoảng cách giữa icon và tên */
    text-decoration: none;
    color: #333;
    font-weight: bold;
    cursor: pointer; /* Thêm con trỏ pointer khi hover */
    transition: color 0.3s ease; /* Hiệu ứng màu mượt mà khi hover */
}

    .account-icon:hover {
        color: #007bff; /* Màu khi hover */
    }

    .account-icon i {
        font-size: 24px; /* Điều chỉnh kích thước icon */
    }

    .account-icon span {
        font-size: 16px;
    }

/* Dropdown menu */
.account-dropdown {
    position: relative;
    display: inline-block;
}

/* Dropdown menu */
.dropdown-menu {
    display: none;
    position: absolute;
    top: 100%; /* Đặt menu ngay dưới phần tử icon */
    right: 0; /* Căn phải để menu không vượt quá viền màn hình */
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    min-width: 150px;
    z-index: 10000; /* Thêm z-index cao để dropdown không bị che */
    opacity: 0; /* Ẩn dropdown mặc định */
    visibility: hidden; /* Ẩn dropdown */
    transition: opacity 0.3s ease, visibility 0s 0.3s; /* Thêm hiệu ứng mượt mà */
}

    /* Hiển thị dropdown khi hover vào account-dropdown */
    .account-dropdown:hover .dropdown-menu,
    .dropdown-menu:hover {
        display: block;
        opacity: 1; /* Đưa dropdown về trạng thái rõ ràng */
        visibility: visible; /* Hiển thị dropdown khi hover */
        transition: opacity 0.3s ease, visibility 0s 0s; /* Thêm hiệu ứng */
    }

    /* Kiểu cho các item trong dropdown */
    .dropdown-menu a {
        display: block;
        padding: 10px;
        color: #333;
        text-decoration: none;
    }

        .dropdown-menu a:hover {
            background-color: #f1f1f1;
        }
/* Căn chỉnh menu icon */
.search-bar {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
}

.search-input {
    width: 1000px; /* Làm cho ô input chiếm toàn bộ form */
}

.product-card {
    z-index: 1;
}

.cart-icon {
    z-index: 10; /* Đặt số cao hơn để nó hiển thị trên */
}

.account-icon-container {
    z-index: 1100; /* Đặt số cao hơn để nó hiển thị trên */
}

﻿@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <title><PERSON><PERSON><PERSON></title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/ionicons@latest/dist/ionicons.js"></script>
    <style>
        .icon-large {
            font-size: 1.4rem;
            color: #FF4500;
        }
    </style>
</head>
<body class="bg-light">

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-5">
                <div class="card shadow-lg mt-5 border-0">
                    <!-- Header -->
                    <div class="card-header text-center" style="background-color: #FF4500; color: white;">
                        <h3><PERSON><PERSON>ng <PERSON>p</h3>
                    </div>

                    <div class="card-body">
                        <!-- Thông báo lỗi -->
                        @if (ViewBag.loi != null)
                        {
                                <div class="alert alert-danger text-center">@ViewBag.loi</div>
                        }

                        <form method="post" asp-controller="TaiKhoan" asp-action="Login">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Tên tài khoản</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-white">
                                        <ion-icon name="person-circle-outline" class="icon-large"></ion-icon>
                                    </span>
                                    <input type="text" name="tk" class="form-control" required placeholder="Nhập tên tài khoản" />
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Mật khẩu</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-white">
                                        <ion-icon name="lock-closed-outline" class="icon-large"></ion-icon>
                                    </span>
                                    <input type="password" name="mk" class="form-control" required placeholder="Nhập mật khẩu" />
                                </div>
                            </div>

                            <div class="d-grid mt-3">
                                <button type="submit" class="btn fw-bold text-white" style="background-color: #FF4500;">Đăng Nhập</button>
                            </div>
                        </form>

                        <div class="mt-3 text-center">
                            <a href="/TaiKhoan/DangKy" class="text-decoration-none fw-bold" style="color: #FF4500;">Chưa có tài khoản? Đăng ký ngay</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

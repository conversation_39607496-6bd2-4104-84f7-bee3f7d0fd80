﻿using F4_API.DATA;
using F4_API.Models;
using F4_API.Repository.IRepository;
using Microsoft.EntityFrameworkCore;

namespace F4_API.Repository
{
    public class TaiKhoanRepository : ITaiKhoanRepository
    {
        private readonly AppDbContext _contextApp;
        public TaiKhoanRepository(AppDbContext contextApp)
        {
            _contextApp = contextApp;
        }

        public async Task CreateTaiKhoanAsync(TaiKhoan tk)
        {
            try
            {
                _contextApp.TaiKhoans.Add(tk);
                await _contextApp.SaveChangesAsync();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task DeleteTaiKhoanAsync(Guid id)
        {
            try
            {
                var taiKhoan = await _contextApp.TaiKhoans.FindAsync(id);
                if (taiKhoan == null)
                {
                    throw new Exception("Không tìm thấy tài khoản để xóa.");
                }

                _contextApp.TaiKhoans.Remove(taiKhoan);
                await _contextApp.SaveChangesAsync();
            }
            catch (Exception)
            {
                throw;
            }
        }


        public Task<List<TaiKhoan>> GetAllTaiKhoanAsync()
        {
            return _contextApp.TaiKhoans.ToListAsync();
        }

        public async Task<TaiKhoan> GetByIdChucVuAsync(string username, string password)
        {
            return await _contextApp.TaiKhoans
                .Include(tk => tk.NhanViens)
                .ThenInclude(nv => nv.ChucVu)
                                    .FirstOrDefaultAsync(x => x.Username == username && x.Password == password);
        }



        public async Task<TaiKhoan> GetByIdTaiKhoanAsync(Guid id)
        {
            try
            {
                return await _contextApp.TaiKhoans.FindAsync(id);
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task UpdateTaiKhoanAsync(TaiKhoan tk)
        {
            try
            {
                var existing = await _contextApp.TaiKhoans.FindAsync(tk);
                if (existing == null) throw new Exception("Không tìm thấy tài khoản.");

                // Cập nhật các thuộc tính cần thiết
                _contextApp.Entry(existing).CurrentValues.SetValues(tk);

                await _contextApp.SaveChangesAsync();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<TaiKhoan> GetByUsernameAsync(string username)
        {
            var tk = await _contextApp.TaiKhoans.FirstOrDefaultAsync(tk => tk.Username == username);
            if (tk == null)
            {
                Console.WriteLine($"Không tìm thấy username: {username}");
            }
            return tk;
        }

        public async Task<KhachHang?> GetKhachHangByTaiKhoanIdAsync(Guid taiKhoanId)
        {
            return await _contextApp.KhachHangs
                .FirstOrDefaultAsync(kh => kh.TaiKhoanId == taiKhoanId);
        }

       


    }
}

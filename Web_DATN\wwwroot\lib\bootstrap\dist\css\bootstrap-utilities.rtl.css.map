{"version": 3, "sources": ["../../scss/bootstrap-utilities.scss", "../../scss/mixins/_clearfix.scss", "bootstrap-utilities.css", "../../scss/helpers/_colored-links.scss", "../../scss/_variables.scss", "../../scss/helpers/_ratio.scss", "../../scss/helpers/_position.scss", "../../scss/mixins/_breakpoints.scss", "../../scss/helpers/_stacks.scss", "../../scss/helpers/_visually-hidden.scss", "../../scss/mixins/_visually-hidden.scss", "../../scss/helpers/_stretched-link.scss", "../../scss/helpers/_text-truncation.scss", "../../scss/mixins/_text-truncate.scss", "../../scss/helpers/_vr.scss", "../../scss/mixins/_utilities.scss", "../../scss/utilities/_api.scss"], "names": [], "mappings": "AAAA;;;;;EAAA;ACEE;EACE,cAAA;EACA,WAAA;EACA,WAAA;ACKJ;;ACTE;EACE,cC8EW;AFlEf;ACTM;EAEE,cAAA;ADUR;;AChBE;EACE,cC8EW;AF3Df;AChBM;EAEE,cAAA;ADiBR;;ACvBE;EACE,cC8EW;AFpDf;ACvBM;EAEE,cAAA;ADwBR;;AC9BE;EACE,cC8EW;AF7Cf;AC9BM;EAEE,cAAA;AD+BR;;ACrCE;EACE,cC8EW;AFtCf;ACrCM;EAEE,cAAA;ADsCR;;AC5CE;EACE,cC8EW;AF/Bf;AC5CM;EAEE,cAAA;AD6CR;;ACnDE;EACE,cC8EW;AFxBf;ACnDM;EAEE,cAAA;ADoDR;;AC1DE;EACE,cC8EW;AFjBf;AC1DM;EAEE,cAAA;AD2DR;;AGhEA;EACE,kBAAA;EACA,WAAA;AHmEF;AGjEE;EACE,cAAA;EACA,mCAAA;EACA,WAAA;AHmEJ;AGhEE;EACE,kBAAA;EACA,MAAA;EACA,QAAA;EACA,WAAA;EACA,YAAA;AHkEJ;;AG7DE;EACE,uBAAA;AHgEJ;;AGjEE;EACE,qCAAA;AHoEJ;;AGrEE;EACE,sCAAA;AHwEJ;;AGzEE;EACE,sCAAA;AH4EJ;;AIjGA;EACE,eAAA;EACA,MAAA;EACA,OAAA;EACA,QAAA;EACA,aF0hCkC;AFt7BpC;;AIjGA;EACE,eAAA;EACA,OAAA;EACA,SAAA;EACA,QAAA;EACA,aFkhCkC;AF96BpC;;AI5FI;EACE,wBAAA;EAAA,gBAAA;EACA,MAAA;EACA,aFsgC8B;AFv6BpC;;AK1DI;EDxCA;IACE,wBAAA;IAAA,gBAAA;IACA,MAAA;IACA,aFsgC8B;EFh6BlC;AACF;AKlEI;EDxCA;IACE,wBAAA;IAAA,gBAAA;IACA,MAAA;IACA,aFsgC8B;EFz5BlC;AACF;AKzEI;EDxCA;IACE,wBAAA;IAAA,gBAAA;IACA,MAAA;IACA,aFsgC8B;EFl5BlC;AACF;AKhFI;EDxCA;IACE,wBAAA;IAAA,gBAAA;IACA,MAAA;IACA,aFsgC8B;EF34BlC;AACF;AKvFI;EDxCA;IACE,wBAAA;IAAA,gBAAA;IACA,MAAA;IACA,aFsgC8B;EFp4BlC;AACF;AM5JA;EACE,aAAA;EACA,mBAAA;EACA,mBAAA;EACA,mBAAA;AN8JF;;AM3JA;EACE,aAAA;EACA,cAAA;EACA,sBAAA;EACA,mBAAA;AN8JF;;AOtKA;;ECIE,6BAAA;EACA,qBAAA;EACA,sBAAA;EACA,qBAAA;EACA,uBAAA;EACA,2BAAA;EACA,iCAAA;EACA,8BAAA;EACA,oBAAA;ARuKF;;ASlLE;EACE,kBAAA;EACA,MAAA;EACA,OAAA;EACA,SAAA;EACA,QAAA;EACA,UP8asC;EO7atC,WAAA;ATqLJ;;AU7LA;ECAE,gBAAA;EACA,uBAAA;EACA,mBAAA;AXiMF;;AYvMA;EACE,qBAAA;EACA,mBAAA;EACA,UAAA;EACA,eAAA;EACA,8BAAA;EACA,aVuoB4B;AF7b9B;;AajJQ;EAOI,mCAAA;Ab8IZ;;AarJQ;EAOI,8BAAA;AbkJZ;;AazJQ;EAOI,iCAAA;AbsJZ;;Aa7JQ;EAOI,iCAAA;Ab0JZ;;AajKQ;EAOI,sCAAA;Ab8JZ;;AarKQ;EAOI,mCAAA;AbkKZ;;AazKQ;EAOI,uBAAA;AbsKZ;;Aa7KQ;EAOI,sBAAA;Ab0KZ;;AajLQ;EAOI,sBAAA;Ab8KZ;;AarLQ;EAOI,qBAAA;AbkLZ;;AazLQ;EAOI,wBAAA;AbsLZ;;Aa7LQ;EAOI,uBAAA;Ab0LZ;;AajMQ;EAOI,wBAAA;Ab8LZ;;AarMQ;EAOI,qBAAA;AbkMZ;;AazMQ;EAOI,yBAAA;AbsMZ;;Aa7MQ;EAOI,2BAAA;Ab0MZ;;AajNQ;EAOI,4BAAA;Ab8MZ;;AarNQ;EAOI,2BAAA;AbkNZ;;AazNQ;EAOI,0BAAA;AbsNZ;;Aa7NQ;EAOI,gCAAA;Ab0NZ;;AajOQ;EAOI,yBAAA;Ab8NZ;;AarOQ;EAOI,wBAAA;AbkOZ;;AazOQ;EAOI,yBAAA;AbsOZ;;Aa7OQ;EAOI,6BAAA;Ab0OZ;;AajPQ;EAOI,8BAAA;Ab8OZ;;AarPQ;EAOI,wBAAA;AbkPZ;;AazPQ;EAOI,+BAAA;AbsPZ;;Aa7PQ;EAOI,wBAAA;Ab0PZ;;AajQQ;EAOI,wDAAA;Ab8PZ;;AarQQ;EAOI,8DAAA;AbkQZ;;AazQQ;EAOI,uDAAA;AbsQZ;;Aa7QQ;EAOI,2BAAA;Ab0QZ;;AajRQ;EAOI,2BAAA;Ab8QZ;;AarRQ;EAOI,6BAAA;AbkRZ;;AazRQ;EAOI,6BAAA;AbsRZ;;Aa7RQ;EAOI,0BAAA;Ab0RZ;;AajSQ;EAOI,mCAAA;EAAA,2BAAA;Ab8RZ;;AarSQ;EAOI,iBAAA;AbkSZ;;AazSQ;EAOI,mBAAA;AbsSZ;;Aa7SQ;EAOI,oBAAA;Ab0SZ;;AajTQ;EAOI,oBAAA;Ab8SZ;;AarTQ;EAOI,sBAAA;AbkTZ;;AazTQ;EAOI,uBAAA;AbsTZ;;Aa7TQ;EAOI,mBAAA;Ab0TZ;;AajUQ;EAOI,qBAAA;Ab8TZ;;AarUQ;EAOI,sBAAA;AbkUZ;;AazUQ;EAOI,kBAAA;AbsUZ;;Aa7UQ;EAOI,oBAAA;Ab0UZ;;AajVQ;EAOI,qBAAA;Ab8UZ;;AarVQ;EAOI,0CAAA;AbkVZ;;AazVQ;EAOI,qCAAA;AbsVZ;;Aa7VQ;EAOI,sCAAA;Ab0VZ;;AajWQ;EAOI,oCAAA;Ab8VZ;;AarWQ;EAOI,oBAAA;AbkWZ;;AazWQ;EAOI,wCAAA;AbsWZ;;Aa7WQ;EAOI,wBAAA;Ab0WZ;;AajXQ;EAOI,yCAAA;Ab8WZ;;AarXQ;EAOI,yBAAA;AbkXZ;;AazXQ;EAOI,2CAAA;AbsXZ;;Aa7XQ;EAOI,2BAAA;Ab0XZ;;AajYQ;EAOI,0CAAA;Ab8XZ;;AarYQ;EAOI,0BAAA;AbkYZ;;AazYQ;EAOI,gCAAA;AbsYZ;;Aa7YQ;EAOI,gCAAA;Ab0YZ;;AajZQ;EAOI,gCAAA;Ab8YZ;;AarZQ;EAOI,gCAAA;AbkZZ;;AazZQ;EAOI,gCAAA;AbsZZ;;Aa7ZQ;EAOI,gCAAA;Ab0ZZ;;AajaQ;EAOI,gCAAA;Ab8ZZ;;AaraQ;EAOI,gCAAA;AbkaZ;;AazaQ;EAOI,6BAAA;AbsaZ;;Aa7aQ;EAOI,4BAAA;Ab0aZ;;AajbQ;EAOI,4BAAA;Ab8aZ;;AarbQ;EAOI,4BAAA;AbkbZ;;AazbQ;EAOI,4BAAA;AbsbZ;;Aa7bQ;EAOI,4BAAA;Ab0bZ;;AajcQ;EAOI,qBAAA;Ab8bZ;;AarcQ;EAOI,qBAAA;AbkcZ;;AazcQ;EAOI,qBAAA;AbscZ;;Aa7cQ;EAOI,sBAAA;Ab0cZ;;AajdQ;EAOI,sBAAA;Ab8cZ;;AardQ;EAOI,0BAAA;AbkdZ;;AazdQ;EAOI,uBAAA;AbsdZ;;Aa7dQ;EAOI,2BAAA;Ab0dZ;;AajeQ;EAOI,sBAAA;Ab8dZ;;AareQ;EAOI,sBAAA;AbkeZ;;AazeQ;EAOI,sBAAA;AbseZ;;Aa7eQ;EAOI,uBAAA;Ab0eZ;;AajfQ;EAOI,uBAAA;Ab8eZ;;AarfQ;EAOI,2BAAA;AbkfZ;;AazfQ;EAOI,wBAAA;AbsfZ;;Aa7fQ;EAOI,4BAAA;Ab0fZ;;AajgBQ;EAOI,yBAAA;Ab8fZ;;AargBQ;EAOI,8BAAA;AbkgBZ;;AazgBQ;EAOI,iCAAA;AbsgBZ;;Aa7gBQ;EAOI,sCAAA;Ab0gBZ;;AajhBQ;EAOI,yCAAA;Ab8gBZ;;AarhBQ;EAOI,uBAAA;AbkhBZ;;AazhBQ;EAOI,uBAAA;AbshBZ;;Aa7hBQ;EAOI,yBAAA;Ab0hBZ;;AajiBQ;EAOI,yBAAA;Ab8hBZ;;AariBQ;EAOI,0BAAA;AbkiBZ;;AaziBQ;EAOI,4BAAA;AbsiBZ;;Aa7iBQ;EAOI,kCAAA;Ab0iBZ;;AajjBQ;EAOI,iBAAA;Ab8iBZ;;AarjBQ;EAOI,uBAAA;AbkjBZ;;AazjBQ;EAOI,sBAAA;AbsjBZ;;Aa7jBQ;EAOI,oBAAA;Ab0jBZ;;AajkBQ;EAOI,sBAAA;Ab8jBZ;;AarkBQ;EAOI,oBAAA;AbkkBZ;;AazkBQ;EAOI,sCAAA;AbskBZ;;Aa7kBQ;EAOI,oCAAA;Ab0kBZ;;AajlBQ;EAOI,kCAAA;Ab8kBZ;;AarlBQ;EAOI,yCAAA;AbklBZ;;AazlBQ;EAOI,wCAAA;AbslBZ;;Aa7lBQ;EAOI,wCAAA;Ab0lBZ;;AajmBQ;EAOI,kCAAA;Ab8lBZ;;AarmBQ;EAOI,gCAAA;AbkmBZ;;AazmBQ;EAOI,8BAAA;AbsmBZ;;Aa7mBQ;EAOI,gCAAA;Ab0mBZ;;AajnBQ;EAOI,+BAAA;Ab8mBZ;;AarnBQ;EAOI,oCAAA;AbknBZ;;AaznBQ;EAOI,kCAAA;AbsnBZ;;Aa7nBQ;EAOI,gCAAA;Ab0nBZ;;AajoBQ;EAOI,uCAAA;Ab8nBZ;;AaroBQ;EAOI,sCAAA;AbkoBZ;;AazoBQ;EAOI,iCAAA;AbsoBZ;;Aa7oBQ;EAOI,2BAAA;Ab0oBZ;;AajpBQ;EAOI,iCAAA;Ab8oBZ;;AarpBQ;EAOI,+BAAA;AbkpBZ;;AazpBQ;EAOI,6BAAA;AbspBZ;;Aa7pBQ;EAOI,+BAAA;Ab0pBZ;;AajqBQ;EAOI,8BAAA;Ab8pBZ;;AarqBQ;EAOI,oBAAA;AbkqBZ;;AazqBQ;EAOI,mBAAA;AbsqBZ;;Aa7qBQ;EAOI,mBAAA;Ab0qBZ;;AajrBQ;EAOI,mBAAA;Ab8qBZ;;AarrBQ;EAOI,mBAAA;AbkrBZ;;AazrBQ;EAOI,mBAAA;AbsrBZ;;Aa7rBQ;EAOI,mBAAA;Ab0rBZ;;AajsBQ;EAOI,mBAAA;Ab8rBZ;;AarsBQ;EAOI,oBAAA;AbksBZ;;AazsBQ;EAOI,0BAAA;AbssBZ;;Aa7sBQ;EAOI,yBAAA;Ab0sBZ;;AajtBQ;EAOI,uBAAA;Ab8sBZ;;AartBQ;EAOI,yBAAA;AbktBZ;;AaztBQ;EAOI,uBAAA;AbstBZ;;Aa7tBQ;EAOI,uBAAA;Ab0tBZ;;AajuBQ;EAOI,yBAAA;EAAA,0BAAA;Ab+tBZ;;AatuBQ;EAOI,+BAAA;EAAA,gCAAA;AbouBZ;;Aa3uBQ;EAOI,8BAAA;EAAA,+BAAA;AbyuBZ;;AahvBQ;EAOI,4BAAA;EAAA,6BAAA;Ab8uBZ;;AarvBQ;EAOI,8BAAA;EAAA,+BAAA;AbmvBZ;;Aa1vBQ;EAOI,4BAAA;EAAA,6BAAA;AbwvBZ;;Aa/vBQ;EAOI,4BAAA;EAAA,6BAAA;Ab6vBZ;;AapwBQ;EAOI,wBAAA;EAAA,2BAAA;AbkwBZ;;AazwBQ;EAOI,8BAAA;EAAA,iCAAA;AbuwBZ;;Aa9wBQ;EAOI,6BAAA;EAAA,gCAAA;Ab4wBZ;;AanxBQ;EAOI,2BAAA;EAAA,8BAAA;AbixBZ;;AaxxBQ;EAOI,6BAAA;EAAA,gCAAA;AbsxBZ;;Aa7xBQ;EAOI,2BAAA;EAAA,8BAAA;Ab2xBZ;;AalyBQ;EAOI,2BAAA;EAAA,8BAAA;AbgyBZ;;AavyBQ;EAOI,wBAAA;AboyBZ;;Aa3yBQ;EAOI,8BAAA;AbwyBZ;;Aa/yBQ;EAOI,6BAAA;Ab4yBZ;;AanzBQ;EAOI,2BAAA;AbgzBZ;;AavzBQ;EAOI,6BAAA;AbozBZ;;Aa3zBQ;EAOI,2BAAA;AbwzBZ;;Aa/zBQ;EAOI,2BAAA;Ab4zBZ;;Aan0BQ;EAOI,yBAAA;Abg0BZ;;Aav0BQ;EAOI,+BAAA;Abo0BZ;;Aa30BQ;EAOI,8BAAA;Abw0BZ;;Aa/0BQ;EAOI,4BAAA;Ab40BZ;;Aan1BQ;EAOI,8BAAA;Abg1BZ;;Aav1BQ;EAOI,4BAAA;Abo1BZ;;Aa31BQ;EAOI,4BAAA;Abw1BZ;;Aa/1BQ;EAOI,2BAAA;Ab41BZ;;Aan2BQ;EAOI,iCAAA;Abg2BZ;;Aav2BQ;EAOI,gCAAA;Abo2BZ;;Aa32BQ;EAOI,8BAAA;Abw2BZ;;Aa/2BQ;EAOI,gCAAA;Ab42BZ;;Aan3BQ;EAOI,8BAAA;Abg3BZ;;Aav3BQ;EAOI,8BAAA;Abo3BZ;;Aa33BQ;EAOI,0BAAA;Abw3BZ;;Aa/3BQ;EAOI,gCAAA;Ab43BZ;;Aan4BQ;EAOI,+BAAA;Abg4BZ;;Aav4BQ;EAOI,6BAAA;Abo4BZ;;Aa34BQ;EAOI,+BAAA;Abw4BZ;;Aa/4BQ;EAOI,6BAAA;Ab44BZ;;Aan5BQ;EAOI,6BAAA;Abg5BZ;;Aav5BQ;EAOI,qBAAA;Abo5BZ;;Aa35BQ;EAOI,2BAAA;Abw5BZ;;Aa/5BQ;EAOI,0BAAA;Ab45BZ;;Aan6BQ;EAOI,wBAAA;Abg6BZ;;Aav6BQ;EAOI,0BAAA;Abo6BZ;;Aa36BQ;EAOI,wBAAA;Abw6BZ;;Aa/6BQ;EAOI,0BAAA;EAAA,2BAAA;Ab66BZ;;Aap7BQ;EAOI,gCAAA;EAAA,iCAAA;Abk7BZ;;Aaz7BQ;EAOI,+BAAA;EAAA,gCAAA;Abu7BZ;;Aa97BQ;EAOI,6BAAA;EAAA,8BAAA;Ab47BZ;;Aan8BQ;EAOI,+BAAA;EAAA,gCAAA;Abi8BZ;;Aax8BQ;EAOI,6BAAA;EAAA,8BAAA;Abs8BZ;;Aa78BQ;EAOI,yBAAA;EAAA,4BAAA;Ab28BZ;;Aal9BQ;EAOI,+BAAA;EAAA,kCAAA;Abg9BZ;;Aav9BQ;EAOI,8BAAA;EAAA,iCAAA;Abq9BZ;;Aa59BQ;EAOI,4BAAA;EAAA,+BAAA;Ab09BZ;;Aaj+BQ;EAOI,8BAAA;EAAA,iCAAA;Ab+9BZ;;Aat+BQ;EAOI,4BAAA;EAAA,+BAAA;Abo+BZ;;Aa3+BQ;EAOI,yBAAA;Abw+BZ;;Aa/+BQ;EAOI,+BAAA;Ab4+BZ;;Aan/BQ;EAOI,8BAAA;Abg/BZ;;Aav/BQ;EAOI,4BAAA;Abo/BZ;;Aa3/BQ;EAOI,8BAAA;Abw/BZ;;Aa//BQ;EAOI,4BAAA;Ab4/BZ;;AangCQ;EAOI,0BAAA;AbggCZ;;AavgCQ;EAOI,gCAAA;AbogCZ;;Aa3gCQ;EAOI,+BAAA;AbwgCZ;;Aa/gCQ;EAOI,6BAAA;Ab4gCZ;;AanhCQ;EAOI,+BAAA;AbghCZ;;AavhCQ;EAOI,6BAAA;AbohCZ;;Aa3hCQ;EAOI,4BAAA;AbwhCZ;;Aa/hCQ;EAOI,kCAAA;Ab4hCZ;;AaniCQ;EAOI,iCAAA;AbgiCZ;;AaviCQ;EAOI,+BAAA;AboiCZ;;Aa3iCQ;EAOI,iCAAA;AbwiCZ;;Aa/iCQ;EAOI,+BAAA;Ab4iCZ;;AanjCQ;EAOI,2BAAA;AbgjCZ;;AavjCQ;EAOI,iCAAA;AbojCZ;;Aa3jCQ;EAOI,gCAAA;AbwjCZ;;Aa/jCQ;EAOI,8BAAA;Ab4jCZ;;AankCQ;EAOI,gCAAA;AbgkCZ;;AavkCQ;EAOI,8BAAA;AbokCZ;;Aa3kCQ;EAOI,gDAAA;AbwkCZ;;Aa/kCQ;EAOI,4CAAA;Ab4kCZ;;AanlCQ;EAOI,4CAAA;AbglCZ;;AavlCQ;EAOI,0CAAA;AbolCZ;;Aa3lCQ;EAOI,4CAAA;AbwlCZ;;Aa/lCQ;EAOI,6BAAA;Ab4lCZ;;AanmCQ;EAOI,0BAAA;AbgmCZ;;AavmCQ;EAOI,6BAAA;AbomCZ;;Aa3mCQ;EAOI,6BAAA;AbwmCZ;;Aa/mCQ;EAOI,2BAAA;Ab4mCZ;;AannCQ;EAOI,+BAAA;AbgnCZ;;AavnCQ;EAOI,2BAAA;AbonCZ;;Aa3nCQ;EAOI,2BAAA;AbwnCZ;;Aa/nCQ;EAOI,8BAAA;Ab4nCZ;;AanoCQ;EAOI,yBAAA;AbgoCZ;;AavoCQ;EAOI,4BAAA;AbooCZ;;Aa3oCQ;EAOI,2BAAA;AbwoCZ;;Aa/oCQ;EAOI,yBAAA;Ab4oCZ;;AanpCQ;EAOI,4BAAA;AbgpCZ;;AavpCQ;EAOI,2BAAA;AbopCZ;;Aa3pCQ;EAOI,6BAAA;AbwpCZ;;Aa/pCQ;EAOI,gCAAA;Ab4pCZ;;AanqCQ;EAOI,qCAAA;AbgqCZ;;AavqCQ;EAOI,wCAAA;AboqCZ;;Aa3qCQ;EAOI,oCAAA;AbwqCZ;;Aa/qCQ;EAOI,oCAAA;Ab4qCZ;;AanrCQ;EAOI,qCAAA;AbgrCZ;;AavrCQ;EAOI,8BAAA;AborCZ;;Aa3rCQ;EAOI,8BAAA;AbwrCZ;Aa/rCQ;EAIQ,oBAAA;EAGJ,qEAAA;AbosCZ;;Aa3sCQ;EAIQ,oBAAA;EAGJ,uEAAA;AbysCZ;;AahtCQ;EAIQ,oBAAA;EAGJ,qEAAA;Ab8sCZ;;AartCQ;EAIQ,oBAAA;EAGJ,kEAAA;AbmtCZ;;Aa1tCQ;EAIQ,oBAAA;EAGJ,qEAAA;AbwtCZ;;Aa/tCQ;EAIQ,oBAAA;EAGJ,oEAAA;Ab6tCZ;;AapuCQ;EAIQ,oBAAA;EAGJ,mEAAA;AbkuCZ;;AazuCQ;EAIQ,oBAAA;EAGJ,kEAAA;AbuuCZ;;Aa9uCQ;EAIQ,oBAAA;EAGJ,mEAAA;Ab4uCZ;;AanvCQ;EAIQ,oBAAA;EAGJ,mEAAA;AbivCZ;;AaxvCQ;EAIQ,oBAAA;EAGJ,kEAAA;AbsvCZ;;Aa7vCQ;EAIQ,oBAAA;EAGJ,yBAAA;Ab2vCZ;;AalwCQ;EAIQ,oBAAA;EAGJ,oCAAA;AbgwCZ;;AavwCQ;EAIQ,oBAAA;EAGJ,0CAAA;AbqwCZ;;Aa5wCQ;EAIQ,oBAAA;EAGJ,yBAAA;Ab0wCZ;;Aa3xCQ;EACE,uBAAA;Ab8xCV;;Aa/xCQ;EACE,sBAAA;AbkyCV;;AanyCQ;EACE,uBAAA;AbsyCV;;AavyCQ;EACE,oBAAA;Ab0yCV;;AajyCQ;EAIQ,kBAAA;EAGJ,8EAAA;Ab+xCZ;;AatyCQ;EAIQ,kBAAA;EAGJ,gFAAA;AboyCZ;;Aa3yCQ;EAIQ,kBAAA;EAGJ,8EAAA;AbyyCZ;;AahzCQ;EAIQ,kBAAA;EAGJ,2EAAA;Ab8yCZ;;AarzCQ;EAIQ,kBAAA;EAGJ,8EAAA;AbmzCZ;;Aa1zCQ;EAIQ,kBAAA;EAGJ,6EAAA;AbwzCZ;;Aa/zCQ;EAIQ,kBAAA;EAGJ,4EAAA;Ab6zCZ;;Aap0CQ;EAIQ,kBAAA;EAGJ,2EAAA;Abk0CZ;;Aaz0CQ;EAIQ,kBAAA;EAGJ,4EAAA;Abu0CZ;;Aa90CQ;EAIQ,kBAAA;EAGJ,4EAAA;Ab40CZ;;Aan1CQ;EAIQ,kBAAA;EAGJ,2EAAA;Abi1CZ;;Aax1CQ;EAIQ,kBAAA;EAGJ,wCAAA;Abs1CZ;;Aav2CQ;EACE,oBAAA;Ab02CV;;Aa32CQ;EACE,qBAAA;Ab82CV;;Aa/2CQ;EACE,oBAAA;Abk3CV;;Aan3CQ;EACE,qBAAA;Abs3CV;;Aav3CQ;EACE,kBAAA;Ab03CV;;Aaj3CQ;EAOI,+CAAA;Ab82CZ;;Aar3CQ;EAOI,mCAAA;EAAA,gCAAA;EAAA,2BAAA;Abk3CZ;;Aaz3CQ;EAOI,oCAAA;EAAA,iCAAA;EAAA,4BAAA;Abs3CZ;;Aa73CQ;EAOI,oCAAA;EAAA,iCAAA;EAAA,4BAAA;Ab03CZ;;Aaj4CQ;EAOI,+BAAA;Ab83CZ;;Aar4CQ;EAOI,+BAAA;Abk4CZ;;Aaz4CQ;EAOI,iCAAA;Abs4CZ;;Aa74CQ;EAOI,2BAAA;Ab04CZ;;Aaj5CQ;EAOI,gCAAA;Ab84CZ;;Aar5CQ;EAOI,iCAAA;Abk5CZ;;Aaz5CQ;EAOI,gCAAA;Abs5CZ;;Aa75CQ;EAOI,6BAAA;Ab05CZ;;Aaj6CQ;EAOI,+BAAA;Ab85CZ;;Aar6CQ;EAOI,2CAAA;EAAA,0CAAA;Abm6CZ;;Aa16CQ;EAOI,0CAAA;EAAA,6CAAA;Abw6CZ;;Aa/6CQ;EAOI,6CAAA;EAAA,8CAAA;Ab66CZ;;Aap7CQ;EAOI,8CAAA;EAAA,2CAAA;Abk7CZ;;Aaz7CQ;EAOI,8BAAA;Abs7CZ;;Aa77CQ;EAOI,6BAAA;Ab07CZ;;AKj8CI;EQAI;IAOI,uBAAA;Eb+7CV;;Eat8CM;IAOI,sBAAA;Ebm8CV;;Ea18CM;IAOI,sBAAA;Ebu8CV;;Ea98CM;IAOI,0BAAA;Eb28CV;;Eal9CM;IAOI,gCAAA;Eb+8CV;;Eat9CM;IAOI,yBAAA;Ebm9CV;;Ea19CM;IAOI,wBAAA;Ebu9CV;;Ea99CM;IAOI,yBAAA;Eb29CV;;Eal+CM;IAOI,6BAAA;Eb+9CV;;Eat+CM;IAOI,8BAAA;Ebm+CV;;Ea1+CM;IAOI,wBAAA;Ebu+CV;;Ea9+CM;IAOI,+BAAA;Eb2+CV;;Eal/CM;IAOI,wBAAA;Eb++CV;;Eat/CM;IAOI,yBAAA;Ebm/CV;;Ea1/CM;IAOI,8BAAA;Ebu/CV;;Ea9/CM;IAOI,iCAAA;Eb2/CV;;EalgDM;IAOI,sCAAA;Eb+/CV;;EatgDM;IAOI,yCAAA;EbmgDV;;Ea1gDM;IAOI,uBAAA;EbugDV;;Ea9gDM;IAOI,uBAAA;Eb2gDV;;EalhDM;IAOI,yBAAA;Eb+gDV;;EathDM;IAOI,yBAAA;EbmhDV;;Ea1hDM;IAOI,0BAAA;EbuhDV;;Ea9hDM;IAOI,4BAAA;Eb2hDV;;EaliDM;IAOI,kCAAA;Eb+hDV;;EatiDM;IAOI,iBAAA;EbmiDV;;Ea1iDM;IAOI,uBAAA;EbuiDV;;Ea9iDM;IAOI,sBAAA;Eb2iDV;;EaljDM;IAOI,oBAAA;Eb+iDV;;EatjDM;IAOI,sBAAA;EbmjDV;;Ea1jDM;IAOI,oBAAA;EbujDV;;Ea9jDM;IAOI,sCAAA;Eb2jDV;;EalkDM;IAOI,oCAAA;Eb+jDV;;EatkDM;IAOI,kCAAA;EbmkDV;;Ea1kDM;IAOI,yCAAA;EbukDV;;Ea9kDM;IAOI,wCAAA;Eb2kDV;;EallDM;IAOI,wCAAA;Eb+kDV;;EatlDM;IAOI,kCAAA;EbmlDV;;Ea1lDM;IAOI,gCAAA;EbulDV;;Ea9lDM;IAOI,8BAAA;Eb2lDV;;EalmDM;IAOI,gCAAA;Eb+lDV;;EatmDM;IAOI,+BAAA;EbmmDV;;Ea1mDM;IAOI,oCAAA;EbumDV;;Ea9mDM;IAOI,kCAAA;Eb2mDV;;EalnDM;IAOI,gCAAA;Eb+mDV;;EatnDM;IAOI,uCAAA;EbmnDV;;Ea1nDM;IAOI,sCAAA;EbunDV;;Ea9nDM;IAOI,iCAAA;Eb2nDV;;EaloDM;IAOI,2BAAA;Eb+nDV;;EatoDM;IAOI,iCAAA;EbmoDV;;Ea1oDM;IAOI,+BAAA;EbuoDV;;Ea9oDM;IAOI,6BAAA;Eb2oDV;;EalpDM;IAOI,+BAAA;Eb+oDV;;EatpDM;IAOI,8BAAA;EbmpDV;;Ea1pDM;IAOI,oBAAA;EbupDV;;Ea9pDM;IAOI,mBAAA;Eb2pDV;;EalqDM;IAOI,mBAAA;Eb+pDV;;EatqDM;IAOI,mBAAA;EbmqDV;;Ea1qDM;IAOI,mBAAA;EbuqDV;;Ea9qDM;IAOI,mBAAA;Eb2qDV;;EalrDM;IAOI,mBAAA;Eb+qDV;;EatrDM;IAOI,mBAAA;EbmrDV;;Ea1rDM;IAOI,oBAAA;EburDV;;Ea9rDM;IAOI,0BAAA;Eb2rDV;;EalsDM;IAOI,yBAAA;Eb+rDV;;EatsDM;IAOI,uBAAA;EbmsDV;;Ea1sDM;IAOI,yBAAA;EbusDV;;Ea9sDM;IAOI,uBAAA;Eb2sDV;;EaltDM;IAOI,uBAAA;Eb+sDV;;EattDM;IAOI,yBAAA;IAAA,0BAAA;EbotDV;;Ea3tDM;IAOI,+BAAA;IAAA,gCAAA;EbytDV;;EahuDM;IAOI,8BAAA;IAAA,+BAAA;Eb8tDV;;EaruDM;IAOI,4BAAA;IAAA,6BAAA;EbmuDV;;Ea1uDM;IAOI,8BAAA;IAAA,+BAAA;EbwuDV;;Ea/uDM;IAOI,4BAAA;IAAA,6BAAA;Eb6uDV;;EapvDM;IAOI,4BAAA;IAAA,6BAAA;EbkvDV;;EazvDM;IAOI,wBAAA;IAAA,2BAAA;EbuvDV;;Ea9vDM;IAOI,8BAAA;IAAA,iCAAA;Eb4vDV;;EanwDM;IAOI,6BAAA;IAAA,gCAAA;EbiwDV;;EaxwDM;IAOI,2BAAA;IAAA,8BAAA;EbswDV;;Ea7wDM;IAOI,6BAAA;IAAA,gCAAA;Eb2wDV;;EalxDM;IAOI,2BAAA;IAAA,8BAAA;EbgxDV;;EavxDM;IAOI,2BAAA;IAAA,8BAAA;EbqxDV;;Ea5xDM;IAOI,wBAAA;EbyxDV;;EahyDM;IAOI,8BAAA;Eb6xDV;;EapyDM;IAOI,6BAAA;EbiyDV;;EaxyDM;IAOI,2BAAA;EbqyDV;;Ea5yDM;IAOI,6BAAA;EbyyDV;;EahzDM;IAOI,2BAAA;Eb6yDV;;EapzDM;IAOI,2BAAA;EbizDV;;EaxzDM;IAOI,yBAAA;EbqzDV;;Ea5zDM;IAOI,+BAAA;EbyzDV;;Eah0DM;IAOI,8BAAA;Eb6zDV;;Eap0DM;IAOI,4BAAA;Ebi0DV;;Eax0DM;IAOI,8BAAA;Ebq0DV;;Ea50DM;IAOI,4BAAA;Eby0DV;;Eah1DM;IAOI,4BAAA;Eb60DV;;Eap1DM;IAOI,2BAAA;Ebi1DV;;Eax1DM;IAOI,iCAAA;Ebq1DV;;Ea51DM;IAOI,gCAAA;Eby1DV;;Eah2DM;IAOI,8BAAA;Eb61DV;;Eap2DM;IAOI,gCAAA;Ebi2DV;;Eax2DM;IAOI,8BAAA;Ebq2DV;;Ea52DM;IAOI,8BAAA;Eby2DV;;Eah3DM;IAOI,0BAAA;Eb62DV;;Eap3DM;IAOI,gCAAA;Ebi3DV;;Eax3DM;IAOI,+BAAA;Ebq3DV;;Ea53DM;IAOI,6BAAA;Eby3DV;;Eah4DM;IAOI,+BAAA;Eb63DV;;Eap4DM;IAOI,6BAAA;Ebi4DV;;Eax4DM;IAOI,6BAAA;Ebq4DV;;Ea54DM;IAOI,qBAAA;Eby4DV;;Eah5DM;IAOI,2BAAA;Eb64DV;;Eap5DM;IAOI,0BAAA;Ebi5DV;;Eax5DM;IAOI,wBAAA;Ebq5DV;;Ea55DM;IAOI,0BAAA;Eby5DV;;Eah6DM;IAOI,wBAAA;Eb65DV;;Eap6DM;IAOI,0BAAA;IAAA,2BAAA;Ebk6DV;;Eaz6DM;IAOI,gCAAA;IAAA,iCAAA;Ebu6DV;;Ea96DM;IAOI,+BAAA;IAAA,gCAAA;Eb46DV;;Ean7DM;IAOI,6BAAA;IAAA,8BAAA;Ebi7DV;;Eax7DM;IAOI,+BAAA;IAAA,gCAAA;Ebs7DV;;Ea77DM;IAOI,6BAAA;IAAA,8BAAA;Eb27DV;;Eal8DM;IAOI,yBAAA;IAAA,4BAAA;Ebg8DV;;Eav8DM;IAOI,+BAAA;IAAA,kCAAA;Ebq8DV;;Ea58DM;IAOI,8BAAA;IAAA,iCAAA;Eb08DV;;Eaj9DM;IAOI,4BAAA;IAAA,+BAAA;Eb+8DV;;Eat9DM;IAOI,8BAAA;IAAA,iCAAA;Ebo9DV;;Ea39DM;IAOI,4BAAA;IAAA,+BAAA;Eby9DV;;Eah+DM;IAOI,yBAAA;Eb69DV;;Eap+DM;IAOI,+BAAA;Ebi+DV;;Eax+DM;IAOI,8BAAA;Ebq+DV;;Ea5+DM;IAOI,4BAAA;Eby+DV;;Eah/DM;IAOI,8BAAA;Eb6+DV;;Eap/DM;IAOI,4BAAA;Ebi/DV;;Eax/DM;IAOI,0BAAA;Ebq/DV;;Ea5/DM;IAOI,gCAAA;Eby/DV;;EahgEM;IAOI,+BAAA;Eb6/DV;;EapgEM;IAOI,6BAAA;EbigEV;;EaxgEM;IAOI,+BAAA;EbqgEV;;Ea5gEM;IAOI,6BAAA;EbygEV;;EahhEM;IAOI,4BAAA;Eb6gEV;;EaphEM;IAOI,kCAAA;EbihEV;;EaxhEM;IAOI,iCAAA;EbqhEV;;Ea5hEM;IAOI,+BAAA;EbyhEV;;EahiEM;IAOI,iCAAA;Eb6hEV;;EapiEM;IAOI,+BAAA;EbiiEV;;EaxiEM;IAOI,2BAAA;EbqiEV;;Ea5iEM;IAOI,iCAAA;EbyiEV;;EahjEM;IAOI,gCAAA;Eb6iEV;;EapjEM;IAOI,8BAAA;EbijEV;;EaxjEM;IAOI,gCAAA;EbqjEV;;Ea5jEM;IAOI,8BAAA;EbyjEV;;EahkEM;IAOI,4BAAA;Eb6jEV;;EapkEM;IAOI,2BAAA;EbikEV;;EaxkEM;IAOI,6BAAA;EbqkEV;AACF;AK7kEI;EQAI;IAOI,uBAAA;Eb0kEV;;EajlEM;IAOI,sBAAA;Eb8kEV;;EarlEM;IAOI,sBAAA;EbklEV;;EazlEM;IAOI,0BAAA;EbslEV;;Ea7lEM;IAOI,gCAAA;Eb0lEV;;EajmEM;IAOI,yBAAA;Eb8lEV;;EarmEM;IAOI,wBAAA;EbkmEV;;EazmEM;IAOI,yBAAA;EbsmEV;;Ea7mEM;IAOI,6BAAA;Eb0mEV;;EajnEM;IAOI,8BAAA;Eb8mEV;;EarnEM;IAOI,wBAAA;EbknEV;;EaznEM;IAOI,+BAAA;EbsnEV;;Ea7nEM;IAOI,wBAAA;Eb0nEV;;EajoEM;IAOI,yBAAA;Eb8nEV;;EaroEM;IAOI,8BAAA;EbkoEV;;EazoEM;IAOI,iCAAA;EbsoEV;;Ea7oEM;IAOI,sCAAA;Eb0oEV;;EajpEM;IAOI,yCAAA;Eb8oEV;;EarpEM;IAOI,uBAAA;EbkpEV;;EazpEM;IAOI,uBAAA;EbspEV;;Ea7pEM;IAOI,yBAAA;Eb0pEV;;EajqEM;IAOI,yBAAA;Eb8pEV;;EarqEM;IAOI,0BAAA;EbkqEV;;EazqEM;IAOI,4BAAA;EbsqEV;;Ea7qEM;IAOI,kCAAA;Eb0qEV;;EajrEM;IAOI,iBAAA;Eb8qEV;;EarrEM;IAOI,uBAAA;EbkrEV;;EazrEM;IAOI,sBAAA;EbsrEV;;Ea7rEM;IAOI,oBAAA;Eb0rEV;;EajsEM;IAOI,sBAAA;Eb8rEV;;EarsEM;IAOI,oBAAA;EbksEV;;EazsEM;IAOI,sCAAA;EbssEV;;Ea7sEM;IAOI,oCAAA;Eb0sEV;;EajtEM;IAOI,kCAAA;Eb8sEV;;EartEM;IAOI,yCAAA;EbktEV;;EaztEM;IAOI,wCAAA;EbstEV;;Ea7tEM;IAOI,wCAAA;Eb0tEV;;EajuEM;IAOI,kCAAA;Eb8tEV;;EaruEM;IAOI,gCAAA;EbkuEV;;EazuEM;IAOI,8BAAA;EbsuEV;;Ea7uEM;IAOI,gCAAA;Eb0uEV;;EajvEM;IAOI,+BAAA;Eb8uEV;;EarvEM;IAOI,oCAAA;EbkvEV;;EazvEM;IAOI,kCAAA;EbsvEV;;Ea7vEM;IAOI,gCAAA;Eb0vEV;;EajwEM;IAOI,uCAAA;Eb8vEV;;EarwEM;IAOI,sCAAA;EbkwEV;;EazwEM;IAOI,iCAAA;EbswEV;;Ea7wEM;IAOI,2BAAA;Eb0wEV;;EajxEM;IAOI,iCAAA;Eb8wEV;;EarxEM;IAOI,+BAAA;EbkxEV;;EazxEM;IAOI,6BAAA;EbsxEV;;Ea7xEM;IAOI,+BAAA;Eb0xEV;;EajyEM;IAOI,8BAAA;Eb8xEV;;EaryEM;IAOI,oBAAA;EbkyEV;;EazyEM;IAOI,mBAAA;EbsyEV;;Ea7yEM;IAOI,mBAAA;Eb0yEV;;EajzEM;IAOI,mBAAA;Eb8yEV;;EarzEM;IAOI,mBAAA;EbkzEV;;EazzEM;IAOI,mBAAA;EbszEV;;Ea7zEM;IAOI,mBAAA;Eb0zEV;;Eaj0EM;IAOI,mBAAA;Eb8zEV;;Ear0EM;IAOI,oBAAA;Ebk0EV;;Eaz0EM;IAOI,0BAAA;Ebs0EV;;Ea70EM;IAOI,yBAAA;Eb00EV;;Eaj1EM;IAOI,uBAAA;Eb80EV;;Ear1EM;IAOI,yBAAA;Ebk1EV;;Eaz1EM;IAOI,uBAAA;Ebs1EV;;Ea71EM;IAOI,uBAAA;Eb01EV;;Eaj2EM;IAOI,yBAAA;IAAA,0BAAA;Eb+1EV;;Eat2EM;IAOI,+BAAA;IAAA,gCAAA;Ebo2EV;;Ea32EM;IAOI,8BAAA;IAAA,+BAAA;Eby2EV;;Eah3EM;IAOI,4BAAA;IAAA,6BAAA;Eb82EV;;Ear3EM;IAOI,8BAAA;IAAA,+BAAA;Ebm3EV;;Ea13EM;IAOI,4BAAA;IAAA,6BAAA;Ebw3EV;;Ea/3EM;IAOI,4BAAA;IAAA,6BAAA;Eb63EV;;Eap4EM;IAOI,wBAAA;IAAA,2BAAA;Ebk4EV;;Eaz4EM;IAOI,8BAAA;IAAA,iCAAA;Ebu4EV;;Ea94EM;IAOI,6BAAA;IAAA,gCAAA;Eb44EV;;Ean5EM;IAOI,2BAAA;IAAA,8BAAA;Ebi5EV;;Eax5EM;IAOI,6BAAA;IAAA,gCAAA;Ebs5EV;;Ea75EM;IAOI,2BAAA;IAAA,8BAAA;Eb25EV;;Eal6EM;IAOI,2BAAA;IAAA,8BAAA;Ebg6EV;;Eav6EM;IAOI,wBAAA;Ebo6EV;;Ea36EM;IAOI,8BAAA;Ebw6EV;;Ea/6EM;IAOI,6BAAA;Eb46EV;;Ean7EM;IAOI,2BAAA;Ebg7EV;;Eav7EM;IAOI,6BAAA;Ebo7EV;;Ea37EM;IAOI,2BAAA;Ebw7EV;;Ea/7EM;IAOI,2BAAA;Eb47EV;;Ean8EM;IAOI,yBAAA;Ebg8EV;;Eav8EM;IAOI,+BAAA;Ebo8EV;;Ea38EM;IAOI,8BAAA;Ebw8EV;;Ea/8EM;IAOI,4BAAA;Eb48EV;;Ean9EM;IAOI,8BAAA;Ebg9EV;;Eav9EM;IAOI,4BAAA;Ebo9EV;;Ea39EM;IAOI,4BAAA;Ebw9EV;;Ea/9EM;IAOI,2BAAA;Eb49EV;;Ean+EM;IAOI,iCAAA;Ebg+EV;;Eav+EM;IAOI,gCAAA;Ebo+EV;;Ea3+EM;IAOI,8BAAA;Ebw+EV;;Ea/+EM;IAOI,gCAAA;Eb4+EV;;Ean/EM;IAOI,8BAAA;Ebg/EV;;Eav/EM;IAOI,8BAAA;Ebo/EV;;Ea3/EM;IAOI,0BAAA;Ebw/EV;;Ea//EM;IAOI,gCAAA;Eb4/EV;;EangFM;IAOI,+BAAA;EbggFV;;EavgFM;IAOI,6BAAA;EbogFV;;Ea3gFM;IAOI,+BAAA;EbwgFV;;Ea/gFM;IAOI,6BAAA;Eb4gFV;;EanhFM;IAOI,6BAAA;EbghFV;;EavhFM;IAOI,qBAAA;EbohFV;;Ea3hFM;IAOI,2BAAA;EbwhFV;;Ea/hFM;IAOI,0BAAA;Eb4hFV;;EaniFM;IAOI,wBAAA;EbgiFV;;EaviFM;IAOI,0BAAA;EboiFV;;Ea3iFM;IAOI,wBAAA;EbwiFV;;Ea/iFM;IAOI,0BAAA;IAAA,2BAAA;Eb6iFV;;EapjFM;IAOI,gCAAA;IAAA,iCAAA;EbkjFV;;EazjFM;IAOI,+BAAA;IAAA,gCAAA;EbujFV;;Ea9jFM;IAOI,6BAAA;IAAA,8BAAA;Eb4jFV;;EankFM;IAOI,+BAAA;IAAA,gCAAA;EbikFV;;EaxkFM;IAOI,6BAAA;IAAA,8BAAA;EbskFV;;Ea7kFM;IAOI,yBAAA;IAAA,4BAAA;Eb2kFV;;EallFM;IAOI,+BAAA;IAAA,kCAAA;EbglFV;;EavlFM;IAOI,8BAAA;IAAA,iCAAA;EbqlFV;;Ea5lFM;IAOI,4BAAA;IAAA,+BAAA;Eb0lFV;;EajmFM;IAOI,8BAAA;IAAA,iCAAA;Eb+lFV;;EatmFM;IAOI,4BAAA;IAAA,+BAAA;EbomFV;;Ea3mFM;IAOI,yBAAA;EbwmFV;;Ea/mFM;IAOI,+BAAA;Eb4mFV;;EannFM;IAOI,8BAAA;EbgnFV;;EavnFM;IAOI,4BAAA;EbonFV;;Ea3nFM;IAOI,8BAAA;EbwnFV;;Ea/nFM;IAOI,4BAAA;Eb4nFV;;EanoFM;IAOI,0BAAA;EbgoFV;;EavoFM;IAOI,gCAAA;EbooFV;;Ea3oFM;IAOI,+BAAA;EbwoFV;;Ea/oFM;IAOI,6BAAA;Eb4oFV;;EanpFM;IAOI,+BAAA;EbgpFV;;EavpFM;IAOI,6BAAA;EbopFV;;Ea3pFM;IAOI,4BAAA;EbwpFV;;Ea/pFM;IAOI,kCAAA;Eb4pFV;;EanqFM;IAOI,iCAAA;EbgqFV;;EavqFM;IAOI,+BAAA;EboqFV;;Ea3qFM;IAOI,iCAAA;EbwqFV;;Ea/qFM;IAOI,+BAAA;Eb4qFV;;EanrFM;IAOI,2BAAA;EbgrFV;;EavrFM;IAOI,iCAAA;EborFV;;Ea3rFM;IAOI,gCAAA;EbwrFV;;Ea/rFM;IAOI,8BAAA;Eb4rFV;;EansFM;IAOI,gCAAA;EbgsFV;;EavsFM;IAOI,8BAAA;EbosFV;;Ea3sFM;IAOI,4BAAA;EbwsFV;;Ea/sFM;IAOI,2BAAA;Eb4sFV;;EantFM;IAOI,6BAAA;EbgtFV;AACF;AKxtFI;EQAI;IAOI,uBAAA;EbqtFV;;Ea5tFM;IAOI,sBAAA;EbytFV;;EahuFM;IAOI,sBAAA;Eb6tFV;;EapuFM;IAOI,0BAAA;EbiuFV;;EaxuFM;IAOI,gCAAA;EbquFV;;Ea5uFM;IAOI,yBAAA;EbyuFV;;EahvFM;IAOI,wBAAA;Eb6uFV;;EapvFM;IAOI,yBAAA;EbivFV;;EaxvFM;IAOI,6BAAA;EbqvFV;;Ea5vFM;IAOI,8BAAA;EbyvFV;;EahwFM;IAOI,wBAAA;Eb6vFV;;EapwFM;IAOI,+BAAA;EbiwFV;;EaxwFM;IAOI,wBAAA;EbqwFV;;Ea5wFM;IAOI,yBAAA;EbywFV;;EahxFM;IAOI,8BAAA;Eb6wFV;;EapxFM;IAOI,iCAAA;EbixFV;;EaxxFM;IAOI,sCAAA;EbqxFV;;Ea5xFM;IAOI,yCAAA;EbyxFV;;EahyFM;IAOI,uBAAA;Eb6xFV;;EapyFM;IAOI,uBAAA;EbiyFV;;EaxyFM;IAOI,yBAAA;EbqyFV;;Ea5yFM;IAOI,yBAAA;EbyyFV;;EahzFM;IAOI,0BAAA;Eb6yFV;;EapzFM;IAOI,4BAAA;EbizFV;;EaxzFM;IAOI,kCAAA;EbqzFV;;Ea5zFM;IAOI,iBAAA;EbyzFV;;Eah0FM;IAOI,uBAAA;Eb6zFV;;Eap0FM;IAOI,sBAAA;Ebi0FV;;Eax0FM;IAOI,oBAAA;Ebq0FV;;Ea50FM;IAOI,sBAAA;Eby0FV;;Eah1FM;IAOI,oBAAA;Eb60FV;;Eap1FM;IAOI,sCAAA;Ebi1FV;;Eax1FM;IAOI,oCAAA;Ebq1FV;;Ea51FM;IAOI,kCAAA;Eby1FV;;Eah2FM;IAOI,yCAAA;Eb61FV;;Eap2FM;IAOI,wCAAA;Ebi2FV;;Eax2FM;IAOI,wCAAA;Ebq2FV;;Ea52FM;IAOI,kCAAA;Eby2FV;;Eah3FM;IAOI,gCAAA;Eb62FV;;Eap3FM;IAOI,8BAAA;Ebi3FV;;Eax3FM;IAOI,gCAAA;Ebq3FV;;Ea53FM;IAOI,+BAAA;Eby3FV;;Eah4FM;IAOI,oCAAA;Eb63FV;;Eap4FM;IAOI,kCAAA;Ebi4FV;;Eax4FM;IAOI,gCAAA;Ebq4FV;;Ea54FM;IAOI,uCAAA;Eby4FV;;Eah5FM;IAOI,sCAAA;Eb64FV;;Eap5FM;IAOI,iCAAA;Ebi5FV;;Eax5FM;IAOI,2BAAA;Ebq5FV;;Ea55FM;IAOI,iCAAA;Eby5FV;;Eah6FM;IAOI,+BAAA;Eb65FV;;Eap6FM;IAOI,6BAAA;Ebi6FV;;Eax6FM;IAOI,+BAAA;Ebq6FV;;Ea56FM;IAOI,8BAAA;Eby6FV;;Eah7FM;IAOI,oBAAA;Eb66FV;;Eap7FM;IAOI,mBAAA;Ebi7FV;;Eax7FM;IAOI,mBAAA;Ebq7FV;;Ea57FM;IAOI,mBAAA;Eby7FV;;Eah8FM;IAOI,mBAAA;Eb67FV;;Eap8FM;IAOI,mBAAA;Ebi8FV;;Eax8FM;IAOI,mBAAA;Ebq8FV;;Ea58FM;IAOI,mBAAA;Eby8FV;;Eah9FM;IAOI,oBAAA;Eb68FV;;Eap9FM;IAOI,0BAAA;Ebi9FV;;Eax9FM;IAOI,yBAAA;Ebq9FV;;Ea59FM;IAOI,uBAAA;Eby9FV;;Eah+FM;IAOI,yBAAA;Eb69FV;;Eap+FM;IAOI,uBAAA;Ebi+FV;;Eax+FM;IAOI,uBAAA;Ebq+FV;;Ea5+FM;IAOI,yBAAA;IAAA,0BAAA;Eb0+FV;;Eaj/FM;IAOI,+BAAA;IAAA,gCAAA;Eb++FV;;Eat/FM;IAOI,8BAAA;IAAA,+BAAA;Ebo/FV;;Ea3/FM;IAOI,4BAAA;IAAA,6BAAA;Eby/FV;;EahgGM;IAOI,8BAAA;IAAA,+BAAA;Eb8/FV;;EargGM;IAOI,4BAAA;IAAA,6BAAA;EbmgGV;;Ea1gGM;IAOI,4BAAA;IAAA,6BAAA;EbwgGV;;Ea/gGM;IAOI,wBAAA;IAAA,2BAAA;Eb6gGV;;EaphGM;IAOI,8BAAA;IAAA,iCAAA;EbkhGV;;EazhGM;IAOI,6BAAA;IAAA,gCAAA;EbuhGV;;Ea9hGM;IAOI,2BAAA;IAAA,8BAAA;Eb4hGV;;EaniGM;IAOI,6BAAA;IAAA,gCAAA;EbiiGV;;EaxiGM;IAOI,2BAAA;IAAA,8BAAA;EbsiGV;;Ea7iGM;IAOI,2BAAA;IAAA,8BAAA;Eb2iGV;;EaljGM;IAOI,wBAAA;Eb+iGV;;EatjGM;IAOI,8BAAA;EbmjGV;;Ea1jGM;IAOI,6BAAA;EbujGV;;Ea9jGM;IAOI,2BAAA;Eb2jGV;;EalkGM;IAOI,6BAAA;Eb+jGV;;EatkGM;IAOI,2BAAA;EbmkGV;;Ea1kGM;IAOI,2BAAA;EbukGV;;Ea9kGM;IAOI,yBAAA;Eb2kGV;;EallGM;IAOI,+BAAA;Eb+kGV;;EatlGM;IAOI,8BAAA;EbmlGV;;Ea1lGM;IAOI,4BAAA;EbulGV;;Ea9lGM;IAOI,8BAAA;Eb2lGV;;EalmGM;IAOI,4BAAA;Eb+lGV;;EatmGM;IAOI,4BAAA;EbmmGV;;Ea1mGM;IAOI,2BAAA;EbumGV;;Ea9mGM;IAOI,iCAAA;Eb2mGV;;EalnGM;IAOI,gCAAA;Eb+mGV;;EatnGM;IAOI,8BAAA;EbmnGV;;Ea1nGM;IAOI,gCAAA;EbunGV;;Ea9nGM;IAOI,8BAAA;Eb2nGV;;EaloGM;IAOI,8BAAA;Eb+nGV;;EatoGM;IAOI,0BAAA;EbmoGV;;Ea1oGM;IAOI,gCAAA;EbuoGV;;Ea9oGM;IAOI,+BAAA;Eb2oGV;;EalpGM;IAOI,6BAAA;Eb+oGV;;EatpGM;IAOI,+BAAA;EbmpGV;;Ea1pGM;IAOI,6BAAA;EbupGV;;Ea9pGM;IAOI,6BAAA;Eb2pGV;;EalqGM;IAOI,qBAAA;Eb+pGV;;EatqGM;IAOI,2BAAA;EbmqGV;;Ea1qGM;IAOI,0BAAA;EbuqGV;;Ea9qGM;IAOI,wBAAA;Eb2qGV;;EalrGM;IAOI,0BAAA;Eb+qGV;;EatrGM;IAOI,wBAAA;EbmrGV;;Ea1rGM;IAOI,0BAAA;IAAA,2BAAA;EbwrGV;;Ea/rGM;IAOI,gCAAA;IAAA,iCAAA;Eb6rGV;;EapsGM;IAOI,+BAAA;IAAA,gCAAA;EbksGV;;EazsGM;IAOI,6BAAA;IAAA,8BAAA;EbusGV;;Ea9sGM;IAOI,+BAAA;IAAA,gCAAA;Eb4sGV;;EantGM;IAOI,6BAAA;IAAA,8BAAA;EbitGV;;EaxtGM;IAOI,yBAAA;IAAA,4BAAA;EbstGV;;Ea7tGM;IAOI,+BAAA;IAAA,kCAAA;Eb2tGV;;EaluGM;IAOI,8BAAA;IAAA,iCAAA;EbguGV;;EavuGM;IAOI,4BAAA;IAAA,+BAAA;EbquGV;;Ea5uGM;IAOI,8BAAA;IAAA,iCAAA;Eb0uGV;;EajvGM;IAOI,4BAAA;IAAA,+BAAA;Eb+uGV;;EatvGM;IAOI,yBAAA;EbmvGV;;Ea1vGM;IAOI,+BAAA;EbuvGV;;Ea9vGM;IAOI,8BAAA;Eb2vGV;;EalwGM;IAOI,4BAAA;Eb+vGV;;EatwGM;IAOI,8BAAA;EbmwGV;;Ea1wGM;IAOI,4BAAA;EbuwGV;;Ea9wGM;IAOI,0BAAA;Eb2wGV;;EalxGM;IAOI,gCAAA;Eb+wGV;;EatxGM;IAOI,+BAAA;EbmxGV;;Ea1xGM;IAOI,6BAAA;EbuxGV;;Ea9xGM;IAOI,+BAAA;Eb2xGV;;EalyGM;IAOI,6BAAA;Eb+xGV;;EatyGM;IAOI,4BAAA;EbmyGV;;Ea1yGM;IAOI,kCAAA;EbuyGV;;Ea9yGM;IAOI,iCAAA;Eb2yGV;;EalzGM;IAOI,+BAAA;Eb+yGV;;EatzGM;IAOI,iCAAA;EbmzGV;;Ea1zGM;IAOI,+BAAA;EbuzGV;;Ea9zGM;IAOI,2BAAA;Eb2zGV;;Eal0GM;IAOI,iCAAA;Eb+zGV;;Eat0GM;IAOI,gCAAA;Ebm0GV;;Ea10GM;IAOI,8BAAA;Ebu0GV;;Ea90GM;IAOI,gCAAA;Eb20GV;;Eal1GM;IAOI,8BAAA;Eb+0GV;;Eat1GM;IAOI,4BAAA;Ebm1GV;;Ea11GM;IAOI,2BAAA;Ebu1GV;;Ea91GM;IAOI,6BAAA;Eb21GV;AACF;AKn2GI;EQAI;IAOI,uBAAA;Ebg2GV;;Eav2GM;IAOI,sBAAA;Ebo2GV;;Ea32GM;IAOI,sBAAA;Ebw2GV;;Ea/2GM;IAOI,0BAAA;Eb42GV;;Ean3GM;IAOI,gCAAA;Ebg3GV;;Eav3GM;IAOI,yBAAA;Ebo3GV;;Ea33GM;IAOI,wBAAA;Ebw3GV;;Ea/3GM;IAOI,yBAAA;Eb43GV;;Ean4GM;IAOI,6BAAA;Ebg4GV;;Eav4GM;IAOI,8BAAA;Ebo4GV;;Ea34GM;IAOI,wBAAA;Ebw4GV;;Ea/4GM;IAOI,+BAAA;Eb44GV;;Ean5GM;IAOI,wBAAA;Ebg5GV;;Eav5GM;IAOI,yBAAA;Ebo5GV;;Ea35GM;IAOI,8BAAA;Ebw5GV;;Ea/5GM;IAOI,iCAAA;Eb45GV;;Ean6GM;IAOI,sCAAA;Ebg6GV;;Eav6GM;IAOI,yCAAA;Ebo6GV;;Ea36GM;IAOI,uBAAA;Ebw6GV;;Ea/6GM;IAOI,uBAAA;Eb46GV;;Ean7GM;IAOI,yBAAA;Ebg7GV;;Eav7GM;IAOI,yBAAA;Ebo7GV;;Ea37GM;IAOI,0BAAA;Ebw7GV;;Ea/7GM;IAOI,4BAAA;Eb47GV;;Ean8GM;IAOI,kCAAA;Ebg8GV;;Eav8GM;IAOI,iBAAA;Ebo8GV;;Ea38GM;IAOI,uBAAA;Ebw8GV;;Ea/8GM;IAOI,sBAAA;Eb48GV;;Ean9GM;IAOI,oBAAA;Ebg9GV;;Eav9GM;IAOI,sBAAA;Ebo9GV;;Ea39GM;IAOI,oBAAA;Ebw9GV;;Ea/9GM;IAOI,sCAAA;Eb49GV;;Ean+GM;IAOI,oCAAA;Ebg+GV;;Eav+GM;IAOI,kCAAA;Ebo+GV;;Ea3+GM;IAOI,yCAAA;Ebw+GV;;Ea/+GM;IAOI,wCAAA;Eb4+GV;;Ean/GM;IAOI,wCAAA;Ebg/GV;;Eav/GM;IAOI,kCAAA;Ebo/GV;;Ea3/GM;IAOI,gCAAA;Ebw/GV;;Ea//GM;IAOI,8BAAA;Eb4/GV;;EangHM;IAOI,gCAAA;EbggHV;;EavgHM;IAOI,+BAAA;EbogHV;;Ea3gHM;IAOI,oCAAA;EbwgHV;;Ea/gHM;IAOI,kCAAA;Eb4gHV;;EanhHM;IAOI,gCAAA;EbghHV;;EavhHM;IAOI,uCAAA;EbohHV;;Ea3hHM;IAOI,sCAAA;EbwhHV;;Ea/hHM;IAOI,iCAAA;Eb4hHV;;EaniHM;IAOI,2BAAA;EbgiHV;;EaviHM;IAOI,iCAAA;EboiHV;;Ea3iHM;IAOI,+BAAA;EbwiHV;;Ea/iHM;IAOI,6BAAA;Eb4iHV;;EanjHM;IAOI,+BAAA;EbgjHV;;EavjHM;IAOI,8BAAA;EbojHV;;Ea3jHM;IAOI,oBAAA;EbwjHV;;Ea/jHM;IAOI,mBAAA;Eb4jHV;;EankHM;IAOI,mBAAA;EbgkHV;;EavkHM;IAOI,mBAAA;EbokHV;;Ea3kHM;IAOI,mBAAA;EbwkHV;;Ea/kHM;IAOI,mBAAA;Eb4kHV;;EanlHM;IAOI,mBAAA;EbglHV;;EavlHM;IAOI,mBAAA;EbolHV;;Ea3lHM;IAOI,oBAAA;EbwlHV;;Ea/lHM;IAOI,0BAAA;Eb4lHV;;EanmHM;IAOI,yBAAA;EbgmHV;;EavmHM;IAOI,uBAAA;EbomHV;;Ea3mHM;IAOI,yBAAA;EbwmHV;;Ea/mHM;IAOI,uBAAA;Eb4mHV;;EannHM;IAOI,uBAAA;EbgnHV;;EavnHM;IAOI,yBAAA;IAAA,0BAAA;EbqnHV;;Ea5nHM;IAOI,+BAAA;IAAA,gCAAA;Eb0nHV;;EajoHM;IAOI,8BAAA;IAAA,+BAAA;Eb+nHV;;EatoHM;IAOI,4BAAA;IAAA,6BAAA;EbooHV;;Ea3oHM;IAOI,8BAAA;IAAA,+BAAA;EbyoHV;;EahpHM;IAOI,4BAAA;IAAA,6BAAA;Eb8oHV;;EarpHM;IAOI,4BAAA;IAAA,6BAAA;EbmpHV;;Ea1pHM;IAOI,wBAAA;IAAA,2BAAA;EbwpHV;;Ea/pHM;IAOI,8BAAA;IAAA,iCAAA;Eb6pHV;;EapqHM;IAOI,6BAAA;IAAA,gCAAA;EbkqHV;;EazqHM;IAOI,2BAAA;IAAA,8BAAA;EbuqHV;;Ea9qHM;IAOI,6BAAA;IAAA,gCAAA;Eb4qHV;;EanrHM;IAOI,2BAAA;IAAA,8BAAA;EbirHV;;EaxrHM;IAOI,2BAAA;IAAA,8BAAA;EbsrHV;;Ea7rHM;IAOI,wBAAA;Eb0rHV;;EajsHM;IAOI,8BAAA;Eb8rHV;;EarsHM;IAOI,6BAAA;EbksHV;;EazsHM;IAOI,2BAAA;EbssHV;;Ea7sHM;IAOI,6BAAA;Eb0sHV;;EajtHM;IAOI,2BAAA;Eb8sHV;;EartHM;IAOI,2BAAA;EbktHV;;EaztHM;IAOI,yBAAA;EbstHV;;Ea7tHM;IAOI,+BAAA;Eb0tHV;;EajuHM;IAOI,8BAAA;Eb8tHV;;EaruHM;IAOI,4BAAA;EbkuHV;;EazuHM;IAOI,8BAAA;EbsuHV;;Ea7uHM;IAOI,4BAAA;Eb0uHV;;EajvHM;IAOI,4BAAA;Eb8uHV;;EarvHM;IAOI,2BAAA;EbkvHV;;EazvHM;IAOI,iCAAA;EbsvHV;;Ea7vHM;IAOI,gCAAA;Eb0vHV;;EajwHM;IAOI,8BAAA;Eb8vHV;;EarwHM;IAOI,gCAAA;EbkwHV;;EazwHM;IAOI,8BAAA;EbswHV;;Ea7wHM;IAOI,8BAAA;Eb0wHV;;EajxHM;IAOI,0BAAA;Eb8wHV;;EarxHM;IAOI,gCAAA;EbkxHV;;EazxHM;IAOI,+BAAA;EbsxHV;;Ea7xHM;IAOI,6BAAA;Eb0xHV;;EajyHM;IAOI,+BAAA;Eb8xHV;;EaryHM;IAOI,6BAAA;EbkyHV;;EazyHM;IAOI,6BAAA;EbsyHV;;Ea7yHM;IAOI,qBAAA;Eb0yHV;;EajzHM;IAOI,2BAAA;Eb8yHV;;EarzHM;IAOI,0BAAA;EbkzHV;;EazzHM;IAOI,wBAAA;EbszHV;;Ea7zHM;IAOI,0BAAA;Eb0zHV;;Eaj0HM;IAOI,wBAAA;Eb8zHV;;Ear0HM;IAOI,0BAAA;IAAA,2BAAA;Ebm0HV;;Ea10HM;IAOI,gCAAA;IAAA,iCAAA;Ebw0HV;;Ea/0HM;IAOI,+BAAA;IAAA,gCAAA;Eb60HV;;Eap1HM;IAOI,6BAAA;IAAA,8BAAA;Ebk1HV;;Eaz1HM;IAOI,+BAAA;IAAA,gCAAA;Ebu1HV;;Ea91HM;IAOI,6BAAA;IAAA,8BAAA;Eb41HV;;Ean2HM;IAOI,yBAAA;IAAA,4BAAA;Ebi2HV;;Eax2HM;IAOI,+BAAA;IAAA,kCAAA;Ebs2HV;;Ea72HM;IAOI,8BAAA;IAAA,iCAAA;Eb22HV;;Eal3HM;IAOI,4BAAA;IAAA,+BAAA;Ebg3HV;;Eav3HM;IAOI,8BAAA;IAAA,iCAAA;Ebq3HV;;Ea53HM;IAOI,4BAAA;IAAA,+BAAA;Eb03HV;;Eaj4HM;IAOI,yBAAA;Eb83HV;;Ear4HM;IAOI,+BAAA;Ebk4HV;;Eaz4HM;IAOI,8BAAA;Ebs4HV;;Ea74HM;IAOI,4BAAA;Eb04HV;;Eaj5HM;IAOI,8BAAA;Eb84HV;;Ear5HM;IAOI,4BAAA;Ebk5HV;;Eaz5HM;IAOI,0BAAA;Ebs5HV;;Ea75HM;IAOI,gCAAA;Eb05HV;;Eaj6HM;IAOI,+BAAA;Eb85HV;;Ear6HM;IAOI,6BAAA;Ebk6HV;;Eaz6HM;IAOI,+BAAA;Ebs6HV;;Ea76HM;IAOI,6BAAA;Eb06HV;;Eaj7HM;IAOI,4BAAA;Eb86HV;;Ear7HM;IAOI,kCAAA;Ebk7HV;;Eaz7HM;IAOI,iCAAA;Ebs7HV;;Ea77HM;IAOI,+BAAA;Eb07HV;;Eaj8HM;IAOI,iCAAA;Eb87HV;;Ear8HM;IAOI,+BAAA;Ebk8HV;;Eaz8HM;IAOI,2BAAA;Ebs8HV;;Ea78HM;IAOI,iCAAA;Eb08HV;;Eaj9HM;IAOI,gCAAA;Eb88HV;;Ear9HM;IAOI,8BAAA;Ebk9HV;;Eaz9HM;IAOI,gCAAA;Ebs9HV;;Ea79HM;IAOI,8BAAA;Eb09HV;;Eaj+HM;IAOI,4BAAA;Eb89HV;;Ear+HM;IAOI,2BAAA;Ebk+HV;;Eaz+HM;IAOI,6BAAA;Ebs+HV;AACF;AK9+HI;EQAI;IAOI,uBAAA;Eb2+HV;;Eal/HM;IAOI,sBAAA;Eb++HV;;Eat/HM;IAOI,sBAAA;Ebm/HV;;Ea1/HM;IAOI,0BAAA;Ebu/HV;;Ea9/HM;IAOI,gCAAA;Eb2/HV;;EalgIM;IAOI,yBAAA;Eb+/HV;;EatgIM;IAOI,wBAAA;EbmgIV;;Ea1gIM;IAOI,yBAAA;EbugIV;;Ea9gIM;IAOI,6BAAA;Eb2gIV;;EalhIM;IAOI,8BAAA;Eb+gIV;;EathIM;IAOI,wBAAA;EbmhIV;;Ea1hIM;IAOI,+BAAA;EbuhIV;;Ea9hIM;IAOI,wBAAA;Eb2hIV;;EaliIM;IAOI,yBAAA;Eb+hIV;;EatiIM;IAOI,8BAAA;EbmiIV;;Ea1iIM;IAOI,iCAAA;EbuiIV;;Ea9iIM;IAOI,sCAAA;Eb2iIV;;EaljIM;IAOI,yCAAA;Eb+iIV;;EatjIM;IAOI,uBAAA;EbmjIV;;Ea1jIM;IAOI,uBAAA;EbujIV;;Ea9jIM;IAOI,yBAAA;Eb2jIV;;EalkIM;IAOI,yBAAA;Eb+jIV;;EatkIM;IAOI,0BAAA;EbmkIV;;Ea1kIM;IAOI,4BAAA;EbukIV;;Ea9kIM;IAOI,kCAAA;Eb2kIV;;EallIM;IAOI,iBAAA;Eb+kIV;;EatlIM;IAOI,uBAAA;EbmlIV;;Ea1lIM;IAOI,sBAAA;EbulIV;;Ea9lIM;IAOI,oBAAA;Eb2lIV;;EalmIM;IAOI,sBAAA;Eb+lIV;;EatmIM;IAOI,oBAAA;EbmmIV;;Ea1mIM;IAOI,sCAAA;EbumIV;;Ea9mIM;IAOI,oCAAA;Eb2mIV;;EalnIM;IAOI,kCAAA;Eb+mIV;;EatnIM;IAOI,yCAAA;EbmnIV;;Ea1nIM;IAOI,wCAAA;EbunIV;;Ea9nIM;IAOI,wCAAA;Eb2nIV;;EaloIM;IAOI,kCAAA;Eb+nIV;;EatoIM;IAOI,gCAAA;EbmoIV;;Ea1oIM;IAOI,8BAAA;EbuoIV;;Ea9oIM;IAOI,gCAAA;Eb2oIV;;EalpIM;IAOI,+BAAA;Eb+oIV;;EatpIM;IAOI,oCAAA;EbmpIV;;Ea1pIM;IAOI,kCAAA;EbupIV;;Ea9pIM;IAOI,gCAAA;Eb2pIV;;EalqIM;IAOI,uCAAA;Eb+pIV;;EatqIM;IAOI,sCAAA;EbmqIV;;Ea1qIM;IAOI,iCAAA;EbuqIV;;Ea9qIM;IAOI,2BAAA;Eb2qIV;;EalrIM;IAOI,iCAAA;Eb+qIV;;EatrIM;IAOI,+BAAA;EbmrIV;;Ea1rIM;IAOI,6BAAA;EburIV;;Ea9rIM;IAOI,+BAAA;Eb2rIV;;EalsIM;IAOI,8BAAA;Eb+rIV;;EatsIM;IAOI,oBAAA;EbmsIV;;Ea1sIM;IAOI,mBAAA;EbusIV;;Ea9sIM;IAOI,mBAAA;Eb2sIV;;EaltIM;IAOI,mBAAA;Eb+sIV;;EattIM;IAOI,mBAAA;EbmtIV;;Ea1tIM;IAOI,mBAAA;EbutIV;;Ea9tIM;IAOI,mBAAA;Eb2tIV;;EaluIM;IAOI,mBAAA;Eb+tIV;;EatuIM;IAOI,oBAAA;EbmuIV;;Ea1uIM;IAOI,0BAAA;EbuuIV;;Ea9uIM;IAOI,yBAAA;Eb2uIV;;EalvIM;IAOI,uBAAA;Eb+uIV;;EatvIM;IAOI,yBAAA;EbmvIV;;Ea1vIM;IAOI,uBAAA;EbuvIV;;Ea9vIM;IAOI,uBAAA;Eb2vIV;;EalwIM;IAOI,yBAAA;IAAA,0BAAA;EbgwIV;;EavwIM;IAOI,+BAAA;IAAA,gCAAA;EbqwIV;;Ea5wIM;IAOI,8BAAA;IAAA,+BAAA;Eb0wIV;;EajxIM;IAOI,4BAAA;IAAA,6BAAA;Eb+wIV;;EatxIM;IAOI,8BAAA;IAAA,+BAAA;EboxIV;;Ea3xIM;IAOI,4BAAA;IAAA,6BAAA;EbyxIV;;EahyIM;IAOI,4BAAA;IAAA,6BAAA;Eb8xIV;;EaryIM;IAOI,wBAAA;IAAA,2BAAA;EbmyIV;;Ea1yIM;IAOI,8BAAA;IAAA,iCAAA;EbwyIV;;Ea/yIM;IAOI,6BAAA;IAAA,gCAAA;Eb6yIV;;EapzIM;IAOI,2BAAA;IAAA,8BAAA;EbkzIV;;EazzIM;IAOI,6BAAA;IAAA,gCAAA;EbuzIV;;Ea9zIM;IAOI,2BAAA;IAAA,8BAAA;Eb4zIV;;Ean0IM;IAOI,2BAAA;IAAA,8BAAA;Ebi0IV;;Eax0IM;IAOI,wBAAA;Ebq0IV;;Ea50IM;IAOI,8BAAA;Eby0IV;;Eah1IM;IAOI,6BAAA;Eb60IV;;Eap1IM;IAOI,2BAAA;Ebi1IV;;Eax1IM;IAOI,6BAAA;Ebq1IV;;Ea51IM;IAOI,2BAAA;Eby1IV;;Eah2IM;IAOI,2BAAA;Eb61IV;;Eap2IM;IAOI,yBAAA;Ebi2IV;;Eax2IM;IAOI,+BAAA;Ebq2IV;;Ea52IM;IAOI,8BAAA;Eby2IV;;Eah3IM;IAOI,4BAAA;Eb62IV;;Eap3IM;IAOI,8BAAA;Ebi3IV;;Eax3IM;IAOI,4BAAA;Ebq3IV;;Ea53IM;IAOI,4BAAA;Eby3IV;;Eah4IM;IAOI,2BAAA;Eb63IV;;Eap4IM;IAOI,iCAAA;Ebi4IV;;Eax4IM;IAOI,gCAAA;Ebq4IV;;Ea54IM;IAOI,8BAAA;Eby4IV;;Eah5IM;IAOI,gCAAA;Eb64IV;;Eap5IM;IAOI,8BAAA;Ebi5IV;;Eax5IM;IAOI,8BAAA;Ebq5IV;;Ea55IM;IAOI,0BAAA;Eby5IV;;Eah6IM;IAOI,gCAAA;Eb65IV;;Eap6IM;IAOI,+BAAA;Ebi6IV;;Eax6IM;IAOI,6BAAA;Ebq6IV;;Ea56IM;IAOI,+BAAA;Eby6IV;;Eah7IM;IAOI,6BAAA;Eb66IV;;Eap7IM;IAOI,6BAAA;Ebi7IV;;Eax7IM;IAOI,qBAAA;Ebq7IV;;Ea57IM;IAOI,2BAAA;Eby7IV;;Eah8IM;IAOI,0BAAA;Eb67IV;;Eap8IM;IAOI,wBAAA;Ebi8IV;;Eax8IM;IAOI,0BAAA;Ebq8IV;;Ea58IM;IAOI,wBAAA;Eby8IV;;Eah9IM;IAOI,0BAAA;IAAA,2BAAA;Eb88IV;;Ear9IM;IAOI,gCAAA;IAAA,iCAAA;Ebm9IV;;Ea19IM;IAOI,+BAAA;IAAA,gCAAA;Ebw9IV;;Ea/9IM;IAOI,6BAAA;IAAA,8BAAA;Eb69IV;;Eap+IM;IAOI,+BAAA;IAAA,gCAAA;Ebk+IV;;Eaz+IM;IAOI,6BAAA;IAAA,8BAAA;Ebu+IV;;Ea9+IM;IAOI,yBAAA;IAAA,4BAAA;Eb4+IV;;Ean/IM;IAOI,+BAAA;IAAA,kCAAA;Ebi/IV;;Eax/IM;IAOI,8BAAA;IAAA,iCAAA;Ebs/IV;;Ea7/IM;IAOI,4BAAA;IAAA,+BAAA;Eb2/IV;;EalgJM;IAOI,8BAAA;IAAA,iCAAA;EbggJV;;EavgJM;IAOI,4BAAA;IAAA,+BAAA;EbqgJV;;Ea5gJM;IAOI,yBAAA;EbygJV;;EahhJM;IAOI,+BAAA;Eb6gJV;;EaphJM;IAOI,8BAAA;EbihJV;;EaxhJM;IAOI,4BAAA;EbqhJV;;Ea5hJM;IAOI,8BAAA;EbyhJV;;EahiJM;IAOI,4BAAA;Eb6hJV;;EapiJM;IAOI,0BAAA;EbiiJV;;EaxiJM;IAOI,gCAAA;EbqiJV;;Ea5iJM;IAOI,+BAAA;EbyiJV;;EahjJM;IAOI,6BAAA;Eb6iJV;;EapjJM;IAOI,+BAAA;EbijJV;;EaxjJM;IAOI,6BAAA;EbqjJV;;Ea5jJM;IAOI,4BAAA;EbyjJV;;EahkJM;IAOI,kCAAA;Eb6jJV;;EapkJM;IAOI,iCAAA;EbikJV;;EaxkJM;IAOI,+BAAA;EbqkJV;;Ea5kJM;IAOI,iCAAA;EbykJV;;EahlJM;IAOI,+BAAA;Eb6kJV;;EaplJM;IAOI,2BAAA;EbilJV;;EaxlJM;IAOI,iCAAA;EbqlJV;;Ea5lJM;IAOI,gCAAA;EbylJV;;EahmJM;IAOI,8BAAA;Eb6lJV;;EapmJM;IAOI,gCAAA;EbimJV;;EaxmJM;IAOI,8BAAA;EbqmJV;;Ea5mJM;IAOI,4BAAA;EbymJV;;EahnJM;IAOI,2BAAA;Eb6mJV;;EapnJM;IAOI,6BAAA;EbinJV;AACF;AcrqJA;ED4CQ;IAOI,4BAAA;EbsnJV;;Ea7nJM;IAOI,0BAAA;Eb0nJV;;EajoJM;IAOI,6BAAA;Eb8nJV;;EaroJM;IAOI,4BAAA;EbkoJV;AACF;AcnqJA;EDyBQ;IAOI,0BAAA;EbuoJV;;Ea9oJM;IAOI,gCAAA;Eb2oJV;;EalpJM;IAOI,yBAAA;Eb+oJV;;EatpJM;IAOI,wBAAA;EbmpJV;;Ea1pJM;IAOI,yBAAA;EbupJV;;Ea9pJM;IAOI,6BAAA;Eb2pJV;;EalqJM;IAOI,8BAAA;Eb+pJV;;EatqJM;IAOI,wBAAA;EbmqJV;;Ea1qJM;IAOI,+BAAA;EbuqJV;;Ea9qJM;IAOI,wBAAA;Eb2qJV;AACF", "file": "bootstrap-utilities.rtl.css", "sourcesContent": ["/*!\n * Bootstrap Utilities v5.1.0 (https://getbootstrap.com/)\n * Copyright 2011-2021 The Bootstrap Authors\n * Copyright 2011-2021 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n\n// Configuration\n@import \"functions\";\n@import \"variables\";\n@import \"mixins\";\n@import \"utilities\";\n\n// Helpers\n@import \"helpers\";\n\n// Utilities\n@import \"utilities/api\";\n", "// scss-docs-start clearfix\n@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n// scss-docs-end clearfix\n", "/*!\n * Bootstrap Utilities v5.1.0 (https://getbootstrap.com/)\n * Copyright 2011-2021 The Bootstrap Authors\n * Copyright 2011-2021 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n.clearfix::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.link-primary {\n  color: #0d6efd;\n}\n.link-primary:hover, .link-primary:focus {\n  color: #0a58ca;\n}\n\n.link-secondary {\n  color: #6c757d;\n}\n.link-secondary:hover, .link-secondary:focus {\n  color: #565e64;\n}\n\n.link-success {\n  color: #198754;\n}\n.link-success:hover, .link-success:focus {\n  color: #146c43;\n}\n\n.link-info {\n  color: #0dcaf0;\n}\n.link-info:hover, .link-info:focus {\n  color: #3dd5f3;\n}\n\n.link-warning {\n  color: #ffc107;\n}\n.link-warning:hover, .link-warning:focus {\n  color: #ffcd39;\n}\n\n.link-danger {\n  color: #dc3545;\n}\n.link-danger:hover, .link-danger:focus {\n  color: #b02a37;\n}\n\n.link-light {\n  color: #f8f9fa;\n}\n.link-light:hover, .link-light:focus {\n  color: #f9fafb;\n}\n\n.link-dark {\n  color: #212529;\n}\n.link-dark:hover, .link-dark:focus {\n  color: #1a1e21;\n}\n\n.ratio {\n  position: relative;\n  width: 100%;\n}\n.ratio::before {\n  display: block;\n  padding-top: var(--bs-aspect-ratio);\n  content: \"\";\n}\n.ratio > * {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n\n.ratio-1x1 {\n  --bs-aspect-ratio: 100%;\n}\n\n.ratio-4x3 {\n  --bs-aspect-ratio: calc(3 / 4 * 100%);\n}\n\n.ratio-16x9 {\n  --bs-aspect-ratio: calc(9 / 16 * 100%);\n}\n\n.ratio-21x9 {\n  --bs-aspect-ratio: calc(9 / 21 * 100%);\n}\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1030;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1030;\n}\n\n.sticky-top {\n  position: sticky;\n  top: 0;\n  z-index: 1020;\n}\n\n@media (min-width: 576px) {\n  .sticky-sm-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 768px) {\n  .sticky-md-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 992px) {\n  .sticky-lg-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 1200px) {\n  .sticky-xl-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 1400px) {\n  .sticky-xxl-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n}\n.hstack {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  align-self: stretch;\n}\n\n.vstack {\n  display: flex;\n  flex: 1 1 auto;\n  flex-direction: column;\n  align-self: stretch;\n}\n\n.visually-hidden,\n.visually-hidden-focusable:not(:focus):not(:focus-within) {\n  position: absolute !important;\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important;\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n}\n\n.stretched-link::after {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1;\n  content: \"\";\n}\n\n.text-truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.vr {\n  display: inline-block;\n  align-self: stretch;\n  width: 1px;\n  min-height: 1em;\n  background-color: currentColor;\n  opacity: 0.25;\n}\n\n.align-baseline {\n  vertical-align: baseline !important;\n}\n\n.align-top {\n  vertical-align: top !important;\n}\n\n.align-middle {\n  vertical-align: middle !important;\n}\n\n.align-bottom {\n  vertical-align: bottom !important;\n}\n\n.align-text-bottom {\n  vertical-align: text-bottom !important;\n}\n\n.align-text-top {\n  vertical-align: text-top !important;\n}\n\n.float-start {\n  float: left !important;\n}\n\n.float-end {\n  float: right !important;\n}\n\n.float-none {\n  float: none !important;\n}\n\n.opacity-0 {\n  opacity: 0 !important;\n}\n\n.opacity-25 {\n  opacity: 0.25 !important;\n}\n\n.opacity-50 {\n  opacity: 0.5 !important;\n}\n\n.opacity-75 {\n  opacity: 0.75 !important;\n}\n\n.opacity-100 {\n  opacity: 1 !important;\n}\n\n.overflow-auto {\n  overflow: auto !important;\n}\n\n.overflow-hidden {\n  overflow: hidden !important;\n}\n\n.overflow-visible {\n  overflow: visible !important;\n}\n\n.overflow-scroll {\n  overflow: scroll !important;\n}\n\n.d-inline {\n  display: inline !important;\n}\n\n.d-inline-block {\n  display: inline-block !important;\n}\n\n.d-block {\n  display: block !important;\n}\n\n.d-grid {\n  display: grid !important;\n}\n\n.d-table {\n  display: table !important;\n}\n\n.d-table-row {\n  display: table-row !important;\n}\n\n.d-table-cell {\n  display: table-cell !important;\n}\n\n.d-flex {\n  display: flex !important;\n}\n\n.d-inline-flex {\n  display: inline-flex !important;\n}\n\n.d-none {\n  display: none !important;\n}\n\n.shadow {\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;\n}\n\n.shadow-sm {\n  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;\n}\n\n.shadow-lg {\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;\n}\n\n.shadow-none {\n  box-shadow: none !important;\n}\n\n.position-static {\n  position: static !important;\n}\n\n.position-relative {\n  position: relative !important;\n}\n\n.position-absolute {\n  position: absolute !important;\n}\n\n.position-fixed {\n  position: fixed !important;\n}\n\n.position-sticky {\n  position: sticky !important;\n}\n\n.top-0 {\n  top: 0 !important;\n}\n\n.top-50 {\n  top: 50% !important;\n}\n\n.top-100 {\n  top: 100% !important;\n}\n\n.bottom-0 {\n  bottom: 0 !important;\n}\n\n.bottom-50 {\n  bottom: 50% !important;\n}\n\n.bottom-100 {\n  bottom: 100% !important;\n}\n\n.start-0 {\n  left: 0 !important;\n}\n\n.start-50 {\n  left: 50% !important;\n}\n\n.start-100 {\n  left: 100% !important;\n}\n\n.end-0 {\n  right: 0 !important;\n}\n\n.end-50 {\n  right: 50% !important;\n}\n\n.end-100 {\n  right: 100% !important;\n}\n\n.translate-middle {\n  transform: translate(-50%, -50%) !important;\n}\n\n.translate-middle-x {\n  transform: translateX(-50%) !important;\n}\n\n.translate-middle-y {\n  transform: translateY(-50%) !important;\n}\n\n.border {\n  border: 1px solid #dee2e6 !important;\n}\n\n.border-0 {\n  border: 0 !important;\n}\n\n.border-top {\n  border-top: 1px solid #dee2e6 !important;\n}\n\n.border-top-0 {\n  border-top: 0 !important;\n}\n\n.border-end {\n  border-right: 1px solid #dee2e6 !important;\n}\n\n.border-end-0 {\n  border-right: 0 !important;\n}\n\n.border-bottom {\n  border-bottom: 1px solid #dee2e6 !important;\n}\n\n.border-bottom-0 {\n  border-bottom: 0 !important;\n}\n\n.border-start {\n  border-left: 1px solid #dee2e6 !important;\n}\n\n.border-start-0 {\n  border-left: 0 !important;\n}\n\n.border-primary {\n  border-color: #0d6efd !important;\n}\n\n.border-secondary {\n  border-color: #6c757d !important;\n}\n\n.border-success {\n  border-color: #198754 !important;\n}\n\n.border-info {\n  border-color: #0dcaf0 !important;\n}\n\n.border-warning {\n  border-color: #ffc107 !important;\n}\n\n.border-danger {\n  border-color: #dc3545 !important;\n}\n\n.border-light {\n  border-color: #f8f9fa !important;\n}\n\n.border-dark {\n  border-color: #212529 !important;\n}\n\n.border-white {\n  border-color: #fff !important;\n}\n\n.border-1 {\n  border-width: 1px !important;\n}\n\n.border-2 {\n  border-width: 2px !important;\n}\n\n.border-3 {\n  border-width: 3px !important;\n}\n\n.border-4 {\n  border-width: 4px !important;\n}\n\n.border-5 {\n  border-width: 5px !important;\n}\n\n.w-25 {\n  width: 25% !important;\n}\n\n.w-50 {\n  width: 50% !important;\n}\n\n.w-75 {\n  width: 75% !important;\n}\n\n.w-100 {\n  width: 100% !important;\n}\n\n.w-auto {\n  width: auto !important;\n}\n\n.mw-100 {\n  max-width: 100% !important;\n}\n\n.vw-100 {\n  width: 100vw !important;\n}\n\n.min-vw-100 {\n  min-width: 100vw !important;\n}\n\n.h-25 {\n  height: 25% !important;\n}\n\n.h-50 {\n  height: 50% !important;\n}\n\n.h-75 {\n  height: 75% !important;\n}\n\n.h-100 {\n  height: 100% !important;\n}\n\n.h-auto {\n  height: auto !important;\n}\n\n.mh-100 {\n  max-height: 100% !important;\n}\n\n.vh-100 {\n  height: 100vh !important;\n}\n\n.min-vh-100 {\n  min-height: 100vh !important;\n}\n\n.flex-fill {\n  flex: 1 1 auto !important;\n}\n\n.flex-row {\n  flex-direction: row !important;\n}\n\n.flex-column {\n  flex-direction: column !important;\n}\n\n.flex-row-reverse {\n  flex-direction: row-reverse !important;\n}\n\n.flex-column-reverse {\n  flex-direction: column-reverse !important;\n}\n\n.flex-grow-0 {\n  flex-grow: 0 !important;\n}\n\n.flex-grow-1 {\n  flex-grow: 1 !important;\n}\n\n.flex-shrink-0 {\n  flex-shrink: 0 !important;\n}\n\n.flex-shrink-1 {\n  flex-shrink: 1 !important;\n}\n\n.flex-wrap {\n  flex-wrap: wrap !important;\n}\n\n.flex-nowrap {\n  flex-wrap: nowrap !important;\n}\n\n.flex-wrap-reverse {\n  flex-wrap: wrap-reverse !important;\n}\n\n.gap-0 {\n  gap: 0 !important;\n}\n\n.gap-1 {\n  gap: 0.25rem !important;\n}\n\n.gap-2 {\n  gap: 0.5rem !important;\n}\n\n.gap-3 {\n  gap: 1rem !important;\n}\n\n.gap-4 {\n  gap: 1.5rem !important;\n}\n\n.gap-5 {\n  gap: 3rem !important;\n}\n\n.justify-content-start {\n  justify-content: flex-start !important;\n}\n\n.justify-content-end {\n  justify-content: flex-end !important;\n}\n\n.justify-content-center {\n  justify-content: center !important;\n}\n\n.justify-content-between {\n  justify-content: space-between !important;\n}\n\n.justify-content-around {\n  justify-content: space-around !important;\n}\n\n.justify-content-evenly {\n  justify-content: space-evenly !important;\n}\n\n.align-items-start {\n  align-items: flex-start !important;\n}\n\n.align-items-end {\n  align-items: flex-end !important;\n}\n\n.align-items-center {\n  align-items: center !important;\n}\n\n.align-items-baseline {\n  align-items: baseline !important;\n}\n\n.align-items-stretch {\n  align-items: stretch !important;\n}\n\n.align-content-start {\n  align-content: flex-start !important;\n}\n\n.align-content-end {\n  align-content: flex-end !important;\n}\n\n.align-content-center {\n  align-content: center !important;\n}\n\n.align-content-between {\n  align-content: space-between !important;\n}\n\n.align-content-around {\n  align-content: space-around !important;\n}\n\n.align-content-stretch {\n  align-content: stretch !important;\n}\n\n.align-self-auto {\n  align-self: auto !important;\n}\n\n.align-self-start {\n  align-self: flex-start !important;\n}\n\n.align-self-end {\n  align-self: flex-end !important;\n}\n\n.align-self-center {\n  align-self: center !important;\n}\n\n.align-self-baseline {\n  align-self: baseline !important;\n}\n\n.align-self-stretch {\n  align-self: stretch !important;\n}\n\n.order-first {\n  order: -1 !important;\n}\n\n.order-0 {\n  order: 0 !important;\n}\n\n.order-1 {\n  order: 1 !important;\n}\n\n.order-2 {\n  order: 2 !important;\n}\n\n.order-3 {\n  order: 3 !important;\n}\n\n.order-4 {\n  order: 4 !important;\n}\n\n.order-5 {\n  order: 5 !important;\n}\n\n.order-last {\n  order: 6 !important;\n}\n\n.m-0 {\n  margin: 0 !important;\n}\n\n.m-1 {\n  margin: 0.25rem !important;\n}\n\n.m-2 {\n  margin: 0.5rem !important;\n}\n\n.m-3 {\n  margin: 1rem !important;\n}\n\n.m-4 {\n  margin: 1.5rem !important;\n}\n\n.m-5 {\n  margin: 3rem !important;\n}\n\n.m-auto {\n  margin: auto !important;\n}\n\n.mx-0 {\n  margin-right: 0 !important;\n  margin-left: 0 !important;\n}\n\n.mx-1 {\n  margin-right: 0.25rem !important;\n  margin-left: 0.25rem !important;\n}\n\n.mx-2 {\n  margin-right: 0.5rem !important;\n  margin-left: 0.5rem !important;\n}\n\n.mx-3 {\n  margin-right: 1rem !important;\n  margin-left: 1rem !important;\n}\n\n.mx-4 {\n  margin-right: 1.5rem !important;\n  margin-left: 1.5rem !important;\n}\n\n.mx-5 {\n  margin-right: 3rem !important;\n  margin-left: 3rem !important;\n}\n\n.mx-auto {\n  margin-right: auto !important;\n  margin-left: auto !important;\n}\n\n.my-0 {\n  margin-top: 0 !important;\n  margin-bottom: 0 !important;\n}\n\n.my-1 {\n  margin-top: 0.25rem !important;\n  margin-bottom: 0.25rem !important;\n}\n\n.my-2 {\n  margin-top: 0.5rem !important;\n  margin-bottom: 0.5rem !important;\n}\n\n.my-3 {\n  margin-top: 1rem !important;\n  margin-bottom: 1rem !important;\n}\n\n.my-4 {\n  margin-top: 1.5rem !important;\n  margin-bottom: 1.5rem !important;\n}\n\n.my-5 {\n  margin-top: 3rem !important;\n  margin-bottom: 3rem !important;\n}\n\n.my-auto {\n  margin-top: auto !important;\n  margin-bottom: auto !important;\n}\n\n.mt-0 {\n  margin-top: 0 !important;\n}\n\n.mt-1 {\n  margin-top: 0.25rem !important;\n}\n\n.mt-2 {\n  margin-top: 0.5rem !important;\n}\n\n.mt-3 {\n  margin-top: 1rem !important;\n}\n\n.mt-4 {\n  margin-top: 1.5rem !important;\n}\n\n.mt-5 {\n  margin-top: 3rem !important;\n}\n\n.mt-auto {\n  margin-top: auto !important;\n}\n\n.me-0 {\n  margin-right: 0 !important;\n}\n\n.me-1 {\n  margin-right: 0.25rem !important;\n}\n\n.me-2 {\n  margin-right: 0.5rem !important;\n}\n\n.me-3 {\n  margin-right: 1rem !important;\n}\n\n.me-4 {\n  margin-right: 1.5rem !important;\n}\n\n.me-5 {\n  margin-right: 3rem !important;\n}\n\n.me-auto {\n  margin-right: auto !important;\n}\n\n.mb-0 {\n  margin-bottom: 0 !important;\n}\n\n.mb-1 {\n  margin-bottom: 0.25rem !important;\n}\n\n.mb-2 {\n  margin-bottom: 0.5rem !important;\n}\n\n.mb-3 {\n  margin-bottom: 1rem !important;\n}\n\n.mb-4 {\n  margin-bottom: 1.5rem !important;\n}\n\n.mb-5 {\n  margin-bottom: 3rem !important;\n}\n\n.mb-auto {\n  margin-bottom: auto !important;\n}\n\n.ms-0 {\n  margin-left: 0 !important;\n}\n\n.ms-1 {\n  margin-left: 0.25rem !important;\n}\n\n.ms-2 {\n  margin-left: 0.5rem !important;\n}\n\n.ms-3 {\n  margin-left: 1rem !important;\n}\n\n.ms-4 {\n  margin-left: 1.5rem !important;\n}\n\n.ms-5 {\n  margin-left: 3rem !important;\n}\n\n.ms-auto {\n  margin-left: auto !important;\n}\n\n.p-0 {\n  padding: 0 !important;\n}\n\n.p-1 {\n  padding: 0.25rem !important;\n}\n\n.p-2 {\n  padding: 0.5rem !important;\n}\n\n.p-3 {\n  padding: 1rem !important;\n}\n\n.p-4 {\n  padding: 1.5rem !important;\n}\n\n.p-5 {\n  padding: 3rem !important;\n}\n\n.px-0 {\n  padding-right: 0 !important;\n  padding-left: 0 !important;\n}\n\n.px-1 {\n  padding-right: 0.25rem !important;\n  padding-left: 0.25rem !important;\n}\n\n.px-2 {\n  padding-right: 0.5rem !important;\n  padding-left: 0.5rem !important;\n}\n\n.px-3 {\n  padding-right: 1rem !important;\n  padding-left: 1rem !important;\n}\n\n.px-4 {\n  padding-right: 1.5rem !important;\n  padding-left: 1.5rem !important;\n}\n\n.px-5 {\n  padding-right: 3rem !important;\n  padding-left: 3rem !important;\n}\n\n.py-0 {\n  padding-top: 0 !important;\n  padding-bottom: 0 !important;\n}\n\n.py-1 {\n  padding-top: 0.25rem !important;\n  padding-bottom: 0.25rem !important;\n}\n\n.py-2 {\n  padding-top: 0.5rem !important;\n  padding-bottom: 0.5rem !important;\n}\n\n.py-3 {\n  padding-top: 1rem !important;\n  padding-bottom: 1rem !important;\n}\n\n.py-4 {\n  padding-top: 1.5rem !important;\n  padding-bottom: 1.5rem !important;\n}\n\n.py-5 {\n  padding-top: 3rem !important;\n  padding-bottom: 3rem !important;\n}\n\n.pt-0 {\n  padding-top: 0 !important;\n}\n\n.pt-1 {\n  padding-top: 0.25rem !important;\n}\n\n.pt-2 {\n  padding-top: 0.5rem !important;\n}\n\n.pt-3 {\n  padding-top: 1rem !important;\n}\n\n.pt-4 {\n  padding-top: 1.5rem !important;\n}\n\n.pt-5 {\n  padding-top: 3rem !important;\n}\n\n.pe-0 {\n  padding-right: 0 !important;\n}\n\n.pe-1 {\n  padding-right: 0.25rem !important;\n}\n\n.pe-2 {\n  padding-right: 0.5rem !important;\n}\n\n.pe-3 {\n  padding-right: 1rem !important;\n}\n\n.pe-4 {\n  padding-right: 1.5rem !important;\n}\n\n.pe-5 {\n  padding-right: 3rem !important;\n}\n\n.pb-0 {\n  padding-bottom: 0 !important;\n}\n\n.pb-1 {\n  padding-bottom: 0.25rem !important;\n}\n\n.pb-2 {\n  padding-bottom: 0.5rem !important;\n}\n\n.pb-3 {\n  padding-bottom: 1rem !important;\n}\n\n.pb-4 {\n  padding-bottom: 1.5rem !important;\n}\n\n.pb-5 {\n  padding-bottom: 3rem !important;\n}\n\n.ps-0 {\n  padding-left: 0 !important;\n}\n\n.ps-1 {\n  padding-left: 0.25rem !important;\n}\n\n.ps-2 {\n  padding-left: 0.5rem !important;\n}\n\n.ps-3 {\n  padding-left: 1rem !important;\n}\n\n.ps-4 {\n  padding-left: 1.5rem !important;\n}\n\n.ps-5 {\n  padding-left: 3rem !important;\n}\n\n.font-monospace {\n  font-family: var(--bs-font-monospace) !important;\n}\n\n.fs-1 {\n  font-size: calc(1.375rem + 1.5vw) !important;\n}\n\n.fs-2 {\n  font-size: calc(1.325rem + 0.9vw) !important;\n}\n\n.fs-3 {\n  font-size: calc(1.3rem + 0.6vw) !important;\n}\n\n.fs-4 {\n  font-size: calc(1.275rem + 0.3vw) !important;\n}\n\n.fs-5 {\n  font-size: 1.25rem !important;\n}\n\n.fs-6 {\n  font-size: 1rem !important;\n}\n\n.fst-italic {\n  font-style: italic !important;\n}\n\n.fst-normal {\n  font-style: normal !important;\n}\n\n.fw-light {\n  font-weight: 300 !important;\n}\n\n.fw-lighter {\n  font-weight: lighter !important;\n}\n\n.fw-normal {\n  font-weight: 400 !important;\n}\n\n.fw-bold {\n  font-weight: 700 !important;\n}\n\n.fw-bolder {\n  font-weight: bolder !important;\n}\n\n.lh-1 {\n  line-height: 1 !important;\n}\n\n.lh-sm {\n  line-height: 1.25 !important;\n}\n\n.lh-base {\n  line-height: 1.5 !important;\n}\n\n.lh-lg {\n  line-height: 2 !important;\n}\n\n.text-start {\n  text-align: left !important;\n}\n\n.text-end {\n  text-align: right !important;\n}\n\n.text-center {\n  text-align: center !important;\n}\n\n.text-decoration-none {\n  text-decoration: none !important;\n}\n\n.text-decoration-underline {\n  text-decoration: underline !important;\n}\n\n.text-decoration-line-through {\n  text-decoration: line-through !important;\n}\n\n.text-lowercase {\n  text-transform: lowercase !important;\n}\n\n.text-uppercase {\n  text-transform: uppercase !important;\n}\n\n.text-capitalize {\n  text-transform: capitalize !important;\n}\n\n.text-wrap {\n  white-space: normal !important;\n}\n\n.text-nowrap {\n  white-space: nowrap !important;\n}\n\n/* rtl:begin:remove */\n.text-break {\n  word-wrap: break-word !important;\n  word-break: break-word !important;\n}\n\n/* rtl:end:remove */\n.text-primary {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-secondary {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-secondary-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-success {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-info {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-warning {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-danger {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-light {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-light-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-dark {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-black {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-white {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-body {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-body-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-muted {\n  --bs-text-opacity: 1;\n  color: #6c757d !important;\n}\n\n.text-black-50 {\n  --bs-text-opacity: 1;\n  color: rgba(0, 0, 0, 0.5) !important;\n}\n\n.text-white-50 {\n  --bs-text-opacity: 1;\n  color: rgba(255, 255, 255, 0.5) !important;\n}\n\n.text-reset {\n  --bs-text-opacity: 1;\n  color: inherit !important;\n}\n\n.text-opacity-25 {\n  --bs-text-opacity: 0.25;\n}\n\n.text-opacity-50 {\n  --bs-text-opacity: 0.5;\n}\n\n.text-opacity-75 {\n  --bs-text-opacity: 0.75;\n}\n\n.text-opacity-100 {\n  --bs-text-opacity: 1;\n}\n\n.bg-primary {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-secondary {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-secondary-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-success {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-info {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-warning {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-warning-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-danger {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-light {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-dark {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-black {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-white {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-body {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-body-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-transparent {\n  --bs-bg-opacity: 1;\n  background-color: transparent !important;\n}\n\n.bg-opacity-10 {\n  --bs-bg-opacity: 0.1;\n}\n\n.bg-opacity-25 {\n  --bs-bg-opacity: 0.25;\n}\n\n.bg-opacity-50 {\n  --bs-bg-opacity: 0.5;\n}\n\n.bg-opacity-75 {\n  --bs-bg-opacity: 0.75;\n}\n\n.bg-opacity-100 {\n  --bs-bg-opacity: 1;\n}\n\n.bg-gradient {\n  background-image: var(--bs-gradient) !important;\n}\n\n.user-select-all {\n  user-select: all !important;\n}\n\n.user-select-auto {\n  user-select: auto !important;\n}\n\n.user-select-none {\n  user-select: none !important;\n}\n\n.pe-none {\n  pointer-events: none !important;\n}\n\n.pe-auto {\n  pointer-events: auto !important;\n}\n\n.rounded {\n  border-radius: 0.25rem !important;\n}\n\n.rounded-0 {\n  border-radius: 0 !important;\n}\n\n.rounded-1 {\n  border-radius: 0.2rem !important;\n}\n\n.rounded-2 {\n  border-radius: 0.25rem !important;\n}\n\n.rounded-3 {\n  border-radius: 0.3rem !important;\n}\n\n.rounded-circle {\n  border-radius: 50% !important;\n}\n\n.rounded-pill {\n  border-radius: 50rem !important;\n}\n\n.rounded-top {\n  border-top-left-radius: 0.25rem !important;\n  border-top-right-radius: 0.25rem !important;\n}\n\n.rounded-end {\n  border-top-right-radius: 0.25rem !important;\n  border-bottom-right-radius: 0.25rem !important;\n}\n\n.rounded-bottom {\n  border-bottom-right-radius: 0.25rem !important;\n  border-bottom-left-radius: 0.25rem !important;\n}\n\n.rounded-start {\n  border-bottom-left-radius: 0.25rem !important;\n  border-top-left-radius: 0.25rem !important;\n}\n\n.visible {\n  visibility: visible !important;\n}\n\n.invisible {\n  visibility: hidden !important;\n}\n\n@media (min-width: 576px) {\n  .float-sm-start {\n    float: left !important;\n  }\n\n  .float-sm-end {\n    float: right !important;\n  }\n\n  .float-sm-none {\n    float: none !important;\n  }\n\n  .d-sm-inline {\n    display: inline !important;\n  }\n\n  .d-sm-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-sm-block {\n    display: block !important;\n  }\n\n  .d-sm-grid {\n    display: grid !important;\n  }\n\n  .d-sm-table {\n    display: table !important;\n  }\n\n  .d-sm-table-row {\n    display: table-row !important;\n  }\n\n  .d-sm-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-sm-flex {\n    display: flex !important;\n  }\n\n  .d-sm-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-sm-none {\n    display: none !important;\n  }\n\n  .flex-sm-fill {\n    flex: 1 1 auto !important;\n  }\n\n  .flex-sm-row {\n    flex-direction: row !important;\n  }\n\n  .flex-sm-column {\n    flex-direction: column !important;\n  }\n\n  .flex-sm-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n\n  .flex-sm-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n\n  .flex-sm-grow-0 {\n    flex-grow: 0 !important;\n  }\n\n  .flex-sm-grow-1 {\n    flex-grow: 1 !important;\n  }\n\n  .flex-sm-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n\n  .flex-sm-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n\n  .flex-sm-wrap {\n    flex-wrap: wrap !important;\n  }\n\n  .flex-sm-nowrap {\n    flex-wrap: nowrap !important;\n  }\n\n  .flex-sm-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n\n  .gap-sm-0 {\n    gap: 0 !important;\n  }\n\n  .gap-sm-1 {\n    gap: 0.25rem !important;\n  }\n\n  .gap-sm-2 {\n    gap: 0.5rem !important;\n  }\n\n  .gap-sm-3 {\n    gap: 1rem !important;\n  }\n\n  .gap-sm-4 {\n    gap: 1.5rem !important;\n  }\n\n  .gap-sm-5 {\n    gap: 3rem !important;\n  }\n\n  .justify-content-sm-start {\n    justify-content: flex-start !important;\n  }\n\n  .justify-content-sm-end {\n    justify-content: flex-end !important;\n  }\n\n  .justify-content-sm-center {\n    justify-content: center !important;\n  }\n\n  .justify-content-sm-between {\n    justify-content: space-between !important;\n  }\n\n  .justify-content-sm-around {\n    justify-content: space-around !important;\n  }\n\n  .justify-content-sm-evenly {\n    justify-content: space-evenly !important;\n  }\n\n  .align-items-sm-start {\n    align-items: flex-start !important;\n  }\n\n  .align-items-sm-end {\n    align-items: flex-end !important;\n  }\n\n  .align-items-sm-center {\n    align-items: center !important;\n  }\n\n  .align-items-sm-baseline {\n    align-items: baseline !important;\n  }\n\n  .align-items-sm-stretch {\n    align-items: stretch !important;\n  }\n\n  .align-content-sm-start {\n    align-content: flex-start !important;\n  }\n\n  .align-content-sm-end {\n    align-content: flex-end !important;\n  }\n\n  .align-content-sm-center {\n    align-content: center !important;\n  }\n\n  .align-content-sm-between {\n    align-content: space-between !important;\n  }\n\n  .align-content-sm-around {\n    align-content: space-around !important;\n  }\n\n  .align-content-sm-stretch {\n    align-content: stretch !important;\n  }\n\n  .align-self-sm-auto {\n    align-self: auto !important;\n  }\n\n  .align-self-sm-start {\n    align-self: flex-start !important;\n  }\n\n  .align-self-sm-end {\n    align-self: flex-end !important;\n  }\n\n  .align-self-sm-center {\n    align-self: center !important;\n  }\n\n  .align-self-sm-baseline {\n    align-self: baseline !important;\n  }\n\n  .align-self-sm-stretch {\n    align-self: stretch !important;\n  }\n\n  .order-sm-first {\n    order: -1 !important;\n  }\n\n  .order-sm-0 {\n    order: 0 !important;\n  }\n\n  .order-sm-1 {\n    order: 1 !important;\n  }\n\n  .order-sm-2 {\n    order: 2 !important;\n  }\n\n  .order-sm-3 {\n    order: 3 !important;\n  }\n\n  .order-sm-4 {\n    order: 4 !important;\n  }\n\n  .order-sm-5 {\n    order: 5 !important;\n  }\n\n  .order-sm-last {\n    order: 6 !important;\n  }\n\n  .m-sm-0 {\n    margin: 0 !important;\n  }\n\n  .m-sm-1 {\n    margin: 0.25rem !important;\n  }\n\n  .m-sm-2 {\n    margin: 0.5rem !important;\n  }\n\n  .m-sm-3 {\n    margin: 1rem !important;\n  }\n\n  .m-sm-4 {\n    margin: 1.5rem !important;\n  }\n\n  .m-sm-5 {\n    margin: 3rem !important;\n  }\n\n  .m-sm-auto {\n    margin: auto !important;\n  }\n\n  .mx-sm-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n\n  .mx-sm-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n\n  .mx-sm-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n\n  .mx-sm-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n\n  .mx-sm-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n\n  .mx-sm-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n\n  .mx-sm-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n\n  .my-sm-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n\n  .my-sm-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n\n  .my-sm-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .my-sm-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .my-sm-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .my-sm-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n\n  .my-sm-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n\n  .mt-sm-0 {\n    margin-top: 0 !important;\n  }\n\n  .mt-sm-1 {\n    margin-top: 0.25rem !important;\n  }\n\n  .mt-sm-2 {\n    margin-top: 0.5rem !important;\n  }\n\n  .mt-sm-3 {\n    margin-top: 1rem !important;\n  }\n\n  .mt-sm-4 {\n    margin-top: 1.5rem !important;\n  }\n\n  .mt-sm-5 {\n    margin-top: 3rem !important;\n  }\n\n  .mt-sm-auto {\n    margin-top: auto !important;\n  }\n\n  .me-sm-0 {\n    margin-right: 0 !important;\n  }\n\n  .me-sm-1 {\n    margin-right: 0.25rem !important;\n  }\n\n  .me-sm-2 {\n    margin-right: 0.5rem !important;\n  }\n\n  .me-sm-3 {\n    margin-right: 1rem !important;\n  }\n\n  .me-sm-4 {\n    margin-right: 1.5rem !important;\n  }\n\n  .me-sm-5 {\n    margin-right: 3rem !important;\n  }\n\n  .me-sm-auto {\n    margin-right: auto !important;\n  }\n\n  .mb-sm-0 {\n    margin-bottom: 0 !important;\n  }\n\n  .mb-sm-1 {\n    margin-bottom: 0.25rem !important;\n  }\n\n  .mb-sm-2 {\n    margin-bottom: 0.5rem !important;\n  }\n\n  .mb-sm-3 {\n    margin-bottom: 1rem !important;\n  }\n\n  .mb-sm-4 {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .mb-sm-5 {\n    margin-bottom: 3rem !important;\n  }\n\n  .mb-sm-auto {\n    margin-bottom: auto !important;\n  }\n\n  .ms-sm-0 {\n    margin-left: 0 !important;\n  }\n\n  .ms-sm-1 {\n    margin-left: 0.25rem !important;\n  }\n\n  .ms-sm-2 {\n    margin-left: 0.5rem !important;\n  }\n\n  .ms-sm-3 {\n    margin-left: 1rem !important;\n  }\n\n  .ms-sm-4 {\n    margin-left: 1.5rem !important;\n  }\n\n  .ms-sm-5 {\n    margin-left: 3rem !important;\n  }\n\n  .ms-sm-auto {\n    margin-left: auto !important;\n  }\n\n  .p-sm-0 {\n    padding: 0 !important;\n  }\n\n  .p-sm-1 {\n    padding: 0.25rem !important;\n  }\n\n  .p-sm-2 {\n    padding: 0.5rem !important;\n  }\n\n  .p-sm-3 {\n    padding: 1rem !important;\n  }\n\n  .p-sm-4 {\n    padding: 1.5rem !important;\n  }\n\n  .p-sm-5 {\n    padding: 3rem !important;\n  }\n\n  .px-sm-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n\n  .px-sm-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n\n  .px-sm-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n\n  .px-sm-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n\n  .px-sm-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n\n  .px-sm-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n\n  .py-sm-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n\n  .py-sm-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n\n  .py-sm-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n\n  .py-sm-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n\n  .py-sm-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n\n  .py-sm-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n\n  .pt-sm-0 {\n    padding-top: 0 !important;\n  }\n\n  .pt-sm-1 {\n    padding-top: 0.25rem !important;\n  }\n\n  .pt-sm-2 {\n    padding-top: 0.5rem !important;\n  }\n\n  .pt-sm-3 {\n    padding-top: 1rem !important;\n  }\n\n  .pt-sm-4 {\n    padding-top: 1.5rem !important;\n  }\n\n  .pt-sm-5 {\n    padding-top: 3rem !important;\n  }\n\n  .pe-sm-0 {\n    padding-right: 0 !important;\n  }\n\n  .pe-sm-1 {\n    padding-right: 0.25rem !important;\n  }\n\n  .pe-sm-2 {\n    padding-right: 0.5rem !important;\n  }\n\n  .pe-sm-3 {\n    padding-right: 1rem !important;\n  }\n\n  .pe-sm-4 {\n    padding-right: 1.5rem !important;\n  }\n\n  .pe-sm-5 {\n    padding-right: 3rem !important;\n  }\n\n  .pb-sm-0 {\n    padding-bottom: 0 !important;\n  }\n\n  .pb-sm-1 {\n    padding-bottom: 0.25rem !important;\n  }\n\n  .pb-sm-2 {\n    padding-bottom: 0.5rem !important;\n  }\n\n  .pb-sm-3 {\n    padding-bottom: 1rem !important;\n  }\n\n  .pb-sm-4 {\n    padding-bottom: 1.5rem !important;\n  }\n\n  .pb-sm-5 {\n    padding-bottom: 3rem !important;\n  }\n\n  .ps-sm-0 {\n    padding-left: 0 !important;\n  }\n\n  .ps-sm-1 {\n    padding-left: 0.25rem !important;\n  }\n\n  .ps-sm-2 {\n    padding-left: 0.5rem !important;\n  }\n\n  .ps-sm-3 {\n    padding-left: 1rem !important;\n  }\n\n  .ps-sm-4 {\n    padding-left: 1.5rem !important;\n  }\n\n  .ps-sm-5 {\n    padding-left: 3rem !important;\n  }\n\n  .text-sm-start {\n    text-align: left !important;\n  }\n\n  .text-sm-end {\n    text-align: right !important;\n  }\n\n  .text-sm-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 768px) {\n  .float-md-start {\n    float: left !important;\n  }\n\n  .float-md-end {\n    float: right !important;\n  }\n\n  .float-md-none {\n    float: none !important;\n  }\n\n  .d-md-inline {\n    display: inline !important;\n  }\n\n  .d-md-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-md-block {\n    display: block !important;\n  }\n\n  .d-md-grid {\n    display: grid !important;\n  }\n\n  .d-md-table {\n    display: table !important;\n  }\n\n  .d-md-table-row {\n    display: table-row !important;\n  }\n\n  .d-md-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-md-flex {\n    display: flex !important;\n  }\n\n  .d-md-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-md-none {\n    display: none !important;\n  }\n\n  .flex-md-fill {\n    flex: 1 1 auto !important;\n  }\n\n  .flex-md-row {\n    flex-direction: row !important;\n  }\n\n  .flex-md-column {\n    flex-direction: column !important;\n  }\n\n  .flex-md-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n\n  .flex-md-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n\n  .flex-md-grow-0 {\n    flex-grow: 0 !important;\n  }\n\n  .flex-md-grow-1 {\n    flex-grow: 1 !important;\n  }\n\n  .flex-md-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n\n  .flex-md-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n\n  .flex-md-wrap {\n    flex-wrap: wrap !important;\n  }\n\n  .flex-md-nowrap {\n    flex-wrap: nowrap !important;\n  }\n\n  .flex-md-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n\n  .gap-md-0 {\n    gap: 0 !important;\n  }\n\n  .gap-md-1 {\n    gap: 0.25rem !important;\n  }\n\n  .gap-md-2 {\n    gap: 0.5rem !important;\n  }\n\n  .gap-md-3 {\n    gap: 1rem !important;\n  }\n\n  .gap-md-4 {\n    gap: 1.5rem !important;\n  }\n\n  .gap-md-5 {\n    gap: 3rem !important;\n  }\n\n  .justify-content-md-start {\n    justify-content: flex-start !important;\n  }\n\n  .justify-content-md-end {\n    justify-content: flex-end !important;\n  }\n\n  .justify-content-md-center {\n    justify-content: center !important;\n  }\n\n  .justify-content-md-between {\n    justify-content: space-between !important;\n  }\n\n  .justify-content-md-around {\n    justify-content: space-around !important;\n  }\n\n  .justify-content-md-evenly {\n    justify-content: space-evenly !important;\n  }\n\n  .align-items-md-start {\n    align-items: flex-start !important;\n  }\n\n  .align-items-md-end {\n    align-items: flex-end !important;\n  }\n\n  .align-items-md-center {\n    align-items: center !important;\n  }\n\n  .align-items-md-baseline {\n    align-items: baseline !important;\n  }\n\n  .align-items-md-stretch {\n    align-items: stretch !important;\n  }\n\n  .align-content-md-start {\n    align-content: flex-start !important;\n  }\n\n  .align-content-md-end {\n    align-content: flex-end !important;\n  }\n\n  .align-content-md-center {\n    align-content: center !important;\n  }\n\n  .align-content-md-between {\n    align-content: space-between !important;\n  }\n\n  .align-content-md-around {\n    align-content: space-around !important;\n  }\n\n  .align-content-md-stretch {\n    align-content: stretch !important;\n  }\n\n  .align-self-md-auto {\n    align-self: auto !important;\n  }\n\n  .align-self-md-start {\n    align-self: flex-start !important;\n  }\n\n  .align-self-md-end {\n    align-self: flex-end !important;\n  }\n\n  .align-self-md-center {\n    align-self: center !important;\n  }\n\n  .align-self-md-baseline {\n    align-self: baseline !important;\n  }\n\n  .align-self-md-stretch {\n    align-self: stretch !important;\n  }\n\n  .order-md-first {\n    order: -1 !important;\n  }\n\n  .order-md-0 {\n    order: 0 !important;\n  }\n\n  .order-md-1 {\n    order: 1 !important;\n  }\n\n  .order-md-2 {\n    order: 2 !important;\n  }\n\n  .order-md-3 {\n    order: 3 !important;\n  }\n\n  .order-md-4 {\n    order: 4 !important;\n  }\n\n  .order-md-5 {\n    order: 5 !important;\n  }\n\n  .order-md-last {\n    order: 6 !important;\n  }\n\n  .m-md-0 {\n    margin: 0 !important;\n  }\n\n  .m-md-1 {\n    margin: 0.25rem !important;\n  }\n\n  .m-md-2 {\n    margin: 0.5rem !important;\n  }\n\n  .m-md-3 {\n    margin: 1rem !important;\n  }\n\n  .m-md-4 {\n    margin: 1.5rem !important;\n  }\n\n  .m-md-5 {\n    margin: 3rem !important;\n  }\n\n  .m-md-auto {\n    margin: auto !important;\n  }\n\n  .mx-md-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n\n  .mx-md-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n\n  .mx-md-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n\n  .mx-md-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n\n  .mx-md-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n\n  .mx-md-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n\n  .mx-md-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n\n  .my-md-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n\n  .my-md-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n\n  .my-md-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .my-md-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .my-md-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .my-md-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n\n  .my-md-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n\n  .mt-md-0 {\n    margin-top: 0 !important;\n  }\n\n  .mt-md-1 {\n    margin-top: 0.25rem !important;\n  }\n\n  .mt-md-2 {\n    margin-top: 0.5rem !important;\n  }\n\n  .mt-md-3 {\n    margin-top: 1rem !important;\n  }\n\n  .mt-md-4 {\n    margin-top: 1.5rem !important;\n  }\n\n  .mt-md-5 {\n    margin-top: 3rem !important;\n  }\n\n  .mt-md-auto {\n    margin-top: auto !important;\n  }\n\n  .me-md-0 {\n    margin-right: 0 !important;\n  }\n\n  .me-md-1 {\n    margin-right: 0.25rem !important;\n  }\n\n  .me-md-2 {\n    margin-right: 0.5rem !important;\n  }\n\n  .me-md-3 {\n    margin-right: 1rem !important;\n  }\n\n  .me-md-4 {\n    margin-right: 1.5rem !important;\n  }\n\n  .me-md-5 {\n    margin-right: 3rem !important;\n  }\n\n  .me-md-auto {\n    margin-right: auto !important;\n  }\n\n  .mb-md-0 {\n    margin-bottom: 0 !important;\n  }\n\n  .mb-md-1 {\n    margin-bottom: 0.25rem !important;\n  }\n\n  .mb-md-2 {\n    margin-bottom: 0.5rem !important;\n  }\n\n  .mb-md-3 {\n    margin-bottom: 1rem !important;\n  }\n\n  .mb-md-4 {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .mb-md-5 {\n    margin-bottom: 3rem !important;\n  }\n\n  .mb-md-auto {\n    margin-bottom: auto !important;\n  }\n\n  .ms-md-0 {\n    margin-left: 0 !important;\n  }\n\n  .ms-md-1 {\n    margin-left: 0.25rem !important;\n  }\n\n  .ms-md-2 {\n    margin-left: 0.5rem !important;\n  }\n\n  .ms-md-3 {\n    margin-left: 1rem !important;\n  }\n\n  .ms-md-4 {\n    margin-left: 1.5rem !important;\n  }\n\n  .ms-md-5 {\n    margin-left: 3rem !important;\n  }\n\n  .ms-md-auto {\n    margin-left: auto !important;\n  }\n\n  .p-md-0 {\n    padding: 0 !important;\n  }\n\n  .p-md-1 {\n    padding: 0.25rem !important;\n  }\n\n  .p-md-2 {\n    padding: 0.5rem !important;\n  }\n\n  .p-md-3 {\n    padding: 1rem !important;\n  }\n\n  .p-md-4 {\n    padding: 1.5rem !important;\n  }\n\n  .p-md-5 {\n    padding: 3rem !important;\n  }\n\n  .px-md-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n\n  .px-md-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n\n  .px-md-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n\n  .px-md-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n\n  .px-md-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n\n  .px-md-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n\n  .py-md-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n\n  .py-md-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n\n  .py-md-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n\n  .py-md-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n\n  .py-md-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n\n  .py-md-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n\n  .pt-md-0 {\n    padding-top: 0 !important;\n  }\n\n  .pt-md-1 {\n    padding-top: 0.25rem !important;\n  }\n\n  .pt-md-2 {\n    padding-top: 0.5rem !important;\n  }\n\n  .pt-md-3 {\n    padding-top: 1rem !important;\n  }\n\n  .pt-md-4 {\n    padding-top: 1.5rem !important;\n  }\n\n  .pt-md-5 {\n    padding-top: 3rem !important;\n  }\n\n  .pe-md-0 {\n    padding-right: 0 !important;\n  }\n\n  .pe-md-1 {\n    padding-right: 0.25rem !important;\n  }\n\n  .pe-md-2 {\n    padding-right: 0.5rem !important;\n  }\n\n  .pe-md-3 {\n    padding-right: 1rem !important;\n  }\n\n  .pe-md-4 {\n    padding-right: 1.5rem !important;\n  }\n\n  .pe-md-5 {\n    padding-right: 3rem !important;\n  }\n\n  .pb-md-0 {\n    padding-bottom: 0 !important;\n  }\n\n  .pb-md-1 {\n    padding-bottom: 0.25rem !important;\n  }\n\n  .pb-md-2 {\n    padding-bottom: 0.5rem !important;\n  }\n\n  .pb-md-3 {\n    padding-bottom: 1rem !important;\n  }\n\n  .pb-md-4 {\n    padding-bottom: 1.5rem !important;\n  }\n\n  .pb-md-5 {\n    padding-bottom: 3rem !important;\n  }\n\n  .ps-md-0 {\n    padding-left: 0 !important;\n  }\n\n  .ps-md-1 {\n    padding-left: 0.25rem !important;\n  }\n\n  .ps-md-2 {\n    padding-left: 0.5rem !important;\n  }\n\n  .ps-md-3 {\n    padding-left: 1rem !important;\n  }\n\n  .ps-md-4 {\n    padding-left: 1.5rem !important;\n  }\n\n  .ps-md-5 {\n    padding-left: 3rem !important;\n  }\n\n  .text-md-start {\n    text-align: left !important;\n  }\n\n  .text-md-end {\n    text-align: right !important;\n  }\n\n  .text-md-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 992px) {\n  .float-lg-start {\n    float: left !important;\n  }\n\n  .float-lg-end {\n    float: right !important;\n  }\n\n  .float-lg-none {\n    float: none !important;\n  }\n\n  .d-lg-inline {\n    display: inline !important;\n  }\n\n  .d-lg-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-lg-block {\n    display: block !important;\n  }\n\n  .d-lg-grid {\n    display: grid !important;\n  }\n\n  .d-lg-table {\n    display: table !important;\n  }\n\n  .d-lg-table-row {\n    display: table-row !important;\n  }\n\n  .d-lg-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-lg-flex {\n    display: flex !important;\n  }\n\n  .d-lg-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-lg-none {\n    display: none !important;\n  }\n\n  .flex-lg-fill {\n    flex: 1 1 auto !important;\n  }\n\n  .flex-lg-row {\n    flex-direction: row !important;\n  }\n\n  .flex-lg-column {\n    flex-direction: column !important;\n  }\n\n  .flex-lg-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n\n  .flex-lg-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n\n  .flex-lg-grow-0 {\n    flex-grow: 0 !important;\n  }\n\n  .flex-lg-grow-1 {\n    flex-grow: 1 !important;\n  }\n\n  .flex-lg-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n\n  .flex-lg-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n\n  .flex-lg-wrap {\n    flex-wrap: wrap !important;\n  }\n\n  .flex-lg-nowrap {\n    flex-wrap: nowrap !important;\n  }\n\n  .flex-lg-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n\n  .gap-lg-0 {\n    gap: 0 !important;\n  }\n\n  .gap-lg-1 {\n    gap: 0.25rem !important;\n  }\n\n  .gap-lg-2 {\n    gap: 0.5rem !important;\n  }\n\n  .gap-lg-3 {\n    gap: 1rem !important;\n  }\n\n  .gap-lg-4 {\n    gap: 1.5rem !important;\n  }\n\n  .gap-lg-5 {\n    gap: 3rem !important;\n  }\n\n  .justify-content-lg-start {\n    justify-content: flex-start !important;\n  }\n\n  .justify-content-lg-end {\n    justify-content: flex-end !important;\n  }\n\n  .justify-content-lg-center {\n    justify-content: center !important;\n  }\n\n  .justify-content-lg-between {\n    justify-content: space-between !important;\n  }\n\n  .justify-content-lg-around {\n    justify-content: space-around !important;\n  }\n\n  .justify-content-lg-evenly {\n    justify-content: space-evenly !important;\n  }\n\n  .align-items-lg-start {\n    align-items: flex-start !important;\n  }\n\n  .align-items-lg-end {\n    align-items: flex-end !important;\n  }\n\n  .align-items-lg-center {\n    align-items: center !important;\n  }\n\n  .align-items-lg-baseline {\n    align-items: baseline !important;\n  }\n\n  .align-items-lg-stretch {\n    align-items: stretch !important;\n  }\n\n  .align-content-lg-start {\n    align-content: flex-start !important;\n  }\n\n  .align-content-lg-end {\n    align-content: flex-end !important;\n  }\n\n  .align-content-lg-center {\n    align-content: center !important;\n  }\n\n  .align-content-lg-between {\n    align-content: space-between !important;\n  }\n\n  .align-content-lg-around {\n    align-content: space-around !important;\n  }\n\n  .align-content-lg-stretch {\n    align-content: stretch !important;\n  }\n\n  .align-self-lg-auto {\n    align-self: auto !important;\n  }\n\n  .align-self-lg-start {\n    align-self: flex-start !important;\n  }\n\n  .align-self-lg-end {\n    align-self: flex-end !important;\n  }\n\n  .align-self-lg-center {\n    align-self: center !important;\n  }\n\n  .align-self-lg-baseline {\n    align-self: baseline !important;\n  }\n\n  .align-self-lg-stretch {\n    align-self: stretch !important;\n  }\n\n  .order-lg-first {\n    order: -1 !important;\n  }\n\n  .order-lg-0 {\n    order: 0 !important;\n  }\n\n  .order-lg-1 {\n    order: 1 !important;\n  }\n\n  .order-lg-2 {\n    order: 2 !important;\n  }\n\n  .order-lg-3 {\n    order: 3 !important;\n  }\n\n  .order-lg-4 {\n    order: 4 !important;\n  }\n\n  .order-lg-5 {\n    order: 5 !important;\n  }\n\n  .order-lg-last {\n    order: 6 !important;\n  }\n\n  .m-lg-0 {\n    margin: 0 !important;\n  }\n\n  .m-lg-1 {\n    margin: 0.25rem !important;\n  }\n\n  .m-lg-2 {\n    margin: 0.5rem !important;\n  }\n\n  .m-lg-3 {\n    margin: 1rem !important;\n  }\n\n  .m-lg-4 {\n    margin: 1.5rem !important;\n  }\n\n  .m-lg-5 {\n    margin: 3rem !important;\n  }\n\n  .m-lg-auto {\n    margin: auto !important;\n  }\n\n  .mx-lg-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n\n  .mx-lg-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n\n  .mx-lg-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n\n  .mx-lg-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n\n  .mx-lg-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n\n  .mx-lg-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n\n  .mx-lg-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n\n  .my-lg-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n\n  .my-lg-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n\n  .my-lg-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .my-lg-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .my-lg-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .my-lg-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n\n  .my-lg-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n\n  .mt-lg-0 {\n    margin-top: 0 !important;\n  }\n\n  .mt-lg-1 {\n    margin-top: 0.25rem !important;\n  }\n\n  .mt-lg-2 {\n    margin-top: 0.5rem !important;\n  }\n\n  .mt-lg-3 {\n    margin-top: 1rem !important;\n  }\n\n  .mt-lg-4 {\n    margin-top: 1.5rem !important;\n  }\n\n  .mt-lg-5 {\n    margin-top: 3rem !important;\n  }\n\n  .mt-lg-auto {\n    margin-top: auto !important;\n  }\n\n  .me-lg-0 {\n    margin-right: 0 !important;\n  }\n\n  .me-lg-1 {\n    margin-right: 0.25rem !important;\n  }\n\n  .me-lg-2 {\n    margin-right: 0.5rem !important;\n  }\n\n  .me-lg-3 {\n    margin-right: 1rem !important;\n  }\n\n  .me-lg-4 {\n    margin-right: 1.5rem !important;\n  }\n\n  .me-lg-5 {\n    margin-right: 3rem !important;\n  }\n\n  .me-lg-auto {\n    margin-right: auto !important;\n  }\n\n  .mb-lg-0 {\n    margin-bottom: 0 !important;\n  }\n\n  .mb-lg-1 {\n    margin-bottom: 0.25rem !important;\n  }\n\n  .mb-lg-2 {\n    margin-bottom: 0.5rem !important;\n  }\n\n  .mb-lg-3 {\n    margin-bottom: 1rem !important;\n  }\n\n  .mb-lg-4 {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .mb-lg-5 {\n    margin-bottom: 3rem !important;\n  }\n\n  .mb-lg-auto {\n    margin-bottom: auto !important;\n  }\n\n  .ms-lg-0 {\n    margin-left: 0 !important;\n  }\n\n  .ms-lg-1 {\n    margin-left: 0.25rem !important;\n  }\n\n  .ms-lg-2 {\n    margin-left: 0.5rem !important;\n  }\n\n  .ms-lg-3 {\n    margin-left: 1rem !important;\n  }\n\n  .ms-lg-4 {\n    margin-left: 1.5rem !important;\n  }\n\n  .ms-lg-5 {\n    margin-left: 3rem !important;\n  }\n\n  .ms-lg-auto {\n    margin-left: auto !important;\n  }\n\n  .p-lg-0 {\n    padding: 0 !important;\n  }\n\n  .p-lg-1 {\n    padding: 0.25rem !important;\n  }\n\n  .p-lg-2 {\n    padding: 0.5rem !important;\n  }\n\n  .p-lg-3 {\n    padding: 1rem !important;\n  }\n\n  .p-lg-4 {\n    padding: 1.5rem !important;\n  }\n\n  .p-lg-5 {\n    padding: 3rem !important;\n  }\n\n  .px-lg-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n\n  .px-lg-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n\n  .px-lg-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n\n  .px-lg-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n\n  .px-lg-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n\n  .px-lg-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n\n  .py-lg-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n\n  .py-lg-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n\n  .py-lg-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n\n  .py-lg-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n\n  .py-lg-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n\n  .py-lg-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n\n  .pt-lg-0 {\n    padding-top: 0 !important;\n  }\n\n  .pt-lg-1 {\n    padding-top: 0.25rem !important;\n  }\n\n  .pt-lg-2 {\n    padding-top: 0.5rem !important;\n  }\n\n  .pt-lg-3 {\n    padding-top: 1rem !important;\n  }\n\n  .pt-lg-4 {\n    padding-top: 1.5rem !important;\n  }\n\n  .pt-lg-5 {\n    padding-top: 3rem !important;\n  }\n\n  .pe-lg-0 {\n    padding-right: 0 !important;\n  }\n\n  .pe-lg-1 {\n    padding-right: 0.25rem !important;\n  }\n\n  .pe-lg-2 {\n    padding-right: 0.5rem !important;\n  }\n\n  .pe-lg-3 {\n    padding-right: 1rem !important;\n  }\n\n  .pe-lg-4 {\n    padding-right: 1.5rem !important;\n  }\n\n  .pe-lg-5 {\n    padding-right: 3rem !important;\n  }\n\n  .pb-lg-0 {\n    padding-bottom: 0 !important;\n  }\n\n  .pb-lg-1 {\n    padding-bottom: 0.25rem !important;\n  }\n\n  .pb-lg-2 {\n    padding-bottom: 0.5rem !important;\n  }\n\n  .pb-lg-3 {\n    padding-bottom: 1rem !important;\n  }\n\n  .pb-lg-4 {\n    padding-bottom: 1.5rem !important;\n  }\n\n  .pb-lg-5 {\n    padding-bottom: 3rem !important;\n  }\n\n  .ps-lg-0 {\n    padding-left: 0 !important;\n  }\n\n  .ps-lg-1 {\n    padding-left: 0.25rem !important;\n  }\n\n  .ps-lg-2 {\n    padding-left: 0.5rem !important;\n  }\n\n  .ps-lg-3 {\n    padding-left: 1rem !important;\n  }\n\n  .ps-lg-4 {\n    padding-left: 1.5rem !important;\n  }\n\n  .ps-lg-5 {\n    padding-left: 3rem !important;\n  }\n\n  .text-lg-start {\n    text-align: left !important;\n  }\n\n  .text-lg-end {\n    text-align: right !important;\n  }\n\n  .text-lg-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 1200px) {\n  .float-xl-start {\n    float: left !important;\n  }\n\n  .float-xl-end {\n    float: right !important;\n  }\n\n  .float-xl-none {\n    float: none !important;\n  }\n\n  .d-xl-inline {\n    display: inline !important;\n  }\n\n  .d-xl-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-xl-block {\n    display: block !important;\n  }\n\n  .d-xl-grid {\n    display: grid !important;\n  }\n\n  .d-xl-table {\n    display: table !important;\n  }\n\n  .d-xl-table-row {\n    display: table-row !important;\n  }\n\n  .d-xl-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-xl-flex {\n    display: flex !important;\n  }\n\n  .d-xl-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-xl-none {\n    display: none !important;\n  }\n\n  .flex-xl-fill {\n    flex: 1 1 auto !important;\n  }\n\n  .flex-xl-row {\n    flex-direction: row !important;\n  }\n\n  .flex-xl-column {\n    flex-direction: column !important;\n  }\n\n  .flex-xl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n\n  .flex-xl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n\n  .flex-xl-grow-0 {\n    flex-grow: 0 !important;\n  }\n\n  .flex-xl-grow-1 {\n    flex-grow: 1 !important;\n  }\n\n  .flex-xl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n\n  .flex-xl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n\n  .flex-xl-wrap {\n    flex-wrap: wrap !important;\n  }\n\n  .flex-xl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n\n  .flex-xl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n\n  .gap-xl-0 {\n    gap: 0 !important;\n  }\n\n  .gap-xl-1 {\n    gap: 0.25rem !important;\n  }\n\n  .gap-xl-2 {\n    gap: 0.5rem !important;\n  }\n\n  .gap-xl-3 {\n    gap: 1rem !important;\n  }\n\n  .gap-xl-4 {\n    gap: 1.5rem !important;\n  }\n\n  .gap-xl-5 {\n    gap: 3rem !important;\n  }\n\n  .justify-content-xl-start {\n    justify-content: flex-start !important;\n  }\n\n  .justify-content-xl-end {\n    justify-content: flex-end !important;\n  }\n\n  .justify-content-xl-center {\n    justify-content: center !important;\n  }\n\n  .justify-content-xl-between {\n    justify-content: space-between !important;\n  }\n\n  .justify-content-xl-around {\n    justify-content: space-around !important;\n  }\n\n  .justify-content-xl-evenly {\n    justify-content: space-evenly !important;\n  }\n\n  .align-items-xl-start {\n    align-items: flex-start !important;\n  }\n\n  .align-items-xl-end {\n    align-items: flex-end !important;\n  }\n\n  .align-items-xl-center {\n    align-items: center !important;\n  }\n\n  .align-items-xl-baseline {\n    align-items: baseline !important;\n  }\n\n  .align-items-xl-stretch {\n    align-items: stretch !important;\n  }\n\n  .align-content-xl-start {\n    align-content: flex-start !important;\n  }\n\n  .align-content-xl-end {\n    align-content: flex-end !important;\n  }\n\n  .align-content-xl-center {\n    align-content: center !important;\n  }\n\n  .align-content-xl-between {\n    align-content: space-between !important;\n  }\n\n  .align-content-xl-around {\n    align-content: space-around !important;\n  }\n\n  .align-content-xl-stretch {\n    align-content: stretch !important;\n  }\n\n  .align-self-xl-auto {\n    align-self: auto !important;\n  }\n\n  .align-self-xl-start {\n    align-self: flex-start !important;\n  }\n\n  .align-self-xl-end {\n    align-self: flex-end !important;\n  }\n\n  .align-self-xl-center {\n    align-self: center !important;\n  }\n\n  .align-self-xl-baseline {\n    align-self: baseline !important;\n  }\n\n  .align-self-xl-stretch {\n    align-self: stretch !important;\n  }\n\n  .order-xl-first {\n    order: -1 !important;\n  }\n\n  .order-xl-0 {\n    order: 0 !important;\n  }\n\n  .order-xl-1 {\n    order: 1 !important;\n  }\n\n  .order-xl-2 {\n    order: 2 !important;\n  }\n\n  .order-xl-3 {\n    order: 3 !important;\n  }\n\n  .order-xl-4 {\n    order: 4 !important;\n  }\n\n  .order-xl-5 {\n    order: 5 !important;\n  }\n\n  .order-xl-last {\n    order: 6 !important;\n  }\n\n  .m-xl-0 {\n    margin: 0 !important;\n  }\n\n  .m-xl-1 {\n    margin: 0.25rem !important;\n  }\n\n  .m-xl-2 {\n    margin: 0.5rem !important;\n  }\n\n  .m-xl-3 {\n    margin: 1rem !important;\n  }\n\n  .m-xl-4 {\n    margin: 1.5rem !important;\n  }\n\n  .m-xl-5 {\n    margin: 3rem !important;\n  }\n\n  .m-xl-auto {\n    margin: auto !important;\n  }\n\n  .mx-xl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n\n  .mx-xl-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n\n  .mx-xl-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n\n  .mx-xl-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n\n  .mx-xl-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n\n  .mx-xl-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n\n  .mx-xl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n\n  .my-xl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n\n  .my-xl-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n\n  .my-xl-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .my-xl-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .my-xl-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .my-xl-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n\n  .my-xl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n\n  .mt-xl-0 {\n    margin-top: 0 !important;\n  }\n\n  .mt-xl-1 {\n    margin-top: 0.25rem !important;\n  }\n\n  .mt-xl-2 {\n    margin-top: 0.5rem !important;\n  }\n\n  .mt-xl-3 {\n    margin-top: 1rem !important;\n  }\n\n  .mt-xl-4 {\n    margin-top: 1.5rem !important;\n  }\n\n  .mt-xl-5 {\n    margin-top: 3rem !important;\n  }\n\n  .mt-xl-auto {\n    margin-top: auto !important;\n  }\n\n  .me-xl-0 {\n    margin-right: 0 !important;\n  }\n\n  .me-xl-1 {\n    margin-right: 0.25rem !important;\n  }\n\n  .me-xl-2 {\n    margin-right: 0.5rem !important;\n  }\n\n  .me-xl-3 {\n    margin-right: 1rem !important;\n  }\n\n  .me-xl-4 {\n    margin-right: 1.5rem !important;\n  }\n\n  .me-xl-5 {\n    margin-right: 3rem !important;\n  }\n\n  .me-xl-auto {\n    margin-right: auto !important;\n  }\n\n  .mb-xl-0 {\n    margin-bottom: 0 !important;\n  }\n\n  .mb-xl-1 {\n    margin-bottom: 0.25rem !important;\n  }\n\n  .mb-xl-2 {\n    margin-bottom: 0.5rem !important;\n  }\n\n  .mb-xl-3 {\n    margin-bottom: 1rem !important;\n  }\n\n  .mb-xl-4 {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .mb-xl-5 {\n    margin-bottom: 3rem !important;\n  }\n\n  .mb-xl-auto {\n    margin-bottom: auto !important;\n  }\n\n  .ms-xl-0 {\n    margin-left: 0 !important;\n  }\n\n  .ms-xl-1 {\n    margin-left: 0.25rem !important;\n  }\n\n  .ms-xl-2 {\n    margin-left: 0.5rem !important;\n  }\n\n  .ms-xl-3 {\n    margin-left: 1rem !important;\n  }\n\n  .ms-xl-4 {\n    margin-left: 1.5rem !important;\n  }\n\n  .ms-xl-5 {\n    margin-left: 3rem !important;\n  }\n\n  .ms-xl-auto {\n    margin-left: auto !important;\n  }\n\n  .p-xl-0 {\n    padding: 0 !important;\n  }\n\n  .p-xl-1 {\n    padding: 0.25rem !important;\n  }\n\n  .p-xl-2 {\n    padding: 0.5rem !important;\n  }\n\n  .p-xl-3 {\n    padding: 1rem !important;\n  }\n\n  .p-xl-4 {\n    padding: 1.5rem !important;\n  }\n\n  .p-xl-5 {\n    padding: 3rem !important;\n  }\n\n  .px-xl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n\n  .px-xl-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n\n  .px-xl-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n\n  .px-xl-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n\n  .px-xl-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n\n  .px-xl-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n\n  .py-xl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n\n  .py-xl-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n\n  .py-xl-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n\n  .py-xl-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n\n  .py-xl-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n\n  .py-xl-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n\n  .pt-xl-0 {\n    padding-top: 0 !important;\n  }\n\n  .pt-xl-1 {\n    padding-top: 0.25rem !important;\n  }\n\n  .pt-xl-2 {\n    padding-top: 0.5rem !important;\n  }\n\n  .pt-xl-3 {\n    padding-top: 1rem !important;\n  }\n\n  .pt-xl-4 {\n    padding-top: 1.5rem !important;\n  }\n\n  .pt-xl-5 {\n    padding-top: 3rem !important;\n  }\n\n  .pe-xl-0 {\n    padding-right: 0 !important;\n  }\n\n  .pe-xl-1 {\n    padding-right: 0.25rem !important;\n  }\n\n  .pe-xl-2 {\n    padding-right: 0.5rem !important;\n  }\n\n  .pe-xl-3 {\n    padding-right: 1rem !important;\n  }\n\n  .pe-xl-4 {\n    padding-right: 1.5rem !important;\n  }\n\n  .pe-xl-5 {\n    padding-right: 3rem !important;\n  }\n\n  .pb-xl-0 {\n    padding-bottom: 0 !important;\n  }\n\n  .pb-xl-1 {\n    padding-bottom: 0.25rem !important;\n  }\n\n  .pb-xl-2 {\n    padding-bottom: 0.5rem !important;\n  }\n\n  .pb-xl-3 {\n    padding-bottom: 1rem !important;\n  }\n\n  .pb-xl-4 {\n    padding-bottom: 1.5rem !important;\n  }\n\n  .pb-xl-5 {\n    padding-bottom: 3rem !important;\n  }\n\n  .ps-xl-0 {\n    padding-left: 0 !important;\n  }\n\n  .ps-xl-1 {\n    padding-left: 0.25rem !important;\n  }\n\n  .ps-xl-2 {\n    padding-left: 0.5rem !important;\n  }\n\n  .ps-xl-3 {\n    padding-left: 1rem !important;\n  }\n\n  .ps-xl-4 {\n    padding-left: 1.5rem !important;\n  }\n\n  .ps-xl-5 {\n    padding-left: 3rem !important;\n  }\n\n  .text-xl-start {\n    text-align: left !important;\n  }\n\n  .text-xl-end {\n    text-align: right !important;\n  }\n\n  .text-xl-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 1400px) {\n  .float-xxl-start {\n    float: left !important;\n  }\n\n  .float-xxl-end {\n    float: right !important;\n  }\n\n  .float-xxl-none {\n    float: none !important;\n  }\n\n  .d-xxl-inline {\n    display: inline !important;\n  }\n\n  .d-xxl-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-xxl-block {\n    display: block !important;\n  }\n\n  .d-xxl-grid {\n    display: grid !important;\n  }\n\n  .d-xxl-table {\n    display: table !important;\n  }\n\n  .d-xxl-table-row {\n    display: table-row !important;\n  }\n\n  .d-xxl-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-xxl-flex {\n    display: flex !important;\n  }\n\n  .d-xxl-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-xxl-none {\n    display: none !important;\n  }\n\n  .flex-xxl-fill {\n    flex: 1 1 auto !important;\n  }\n\n  .flex-xxl-row {\n    flex-direction: row !important;\n  }\n\n  .flex-xxl-column {\n    flex-direction: column !important;\n  }\n\n  .flex-xxl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n\n  .flex-xxl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n\n  .flex-xxl-grow-0 {\n    flex-grow: 0 !important;\n  }\n\n  .flex-xxl-grow-1 {\n    flex-grow: 1 !important;\n  }\n\n  .flex-xxl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n\n  .flex-xxl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n\n  .flex-xxl-wrap {\n    flex-wrap: wrap !important;\n  }\n\n  .flex-xxl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n\n  .flex-xxl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n\n  .gap-xxl-0 {\n    gap: 0 !important;\n  }\n\n  .gap-xxl-1 {\n    gap: 0.25rem !important;\n  }\n\n  .gap-xxl-2 {\n    gap: 0.5rem !important;\n  }\n\n  .gap-xxl-3 {\n    gap: 1rem !important;\n  }\n\n  .gap-xxl-4 {\n    gap: 1.5rem !important;\n  }\n\n  .gap-xxl-5 {\n    gap: 3rem !important;\n  }\n\n  .justify-content-xxl-start {\n    justify-content: flex-start !important;\n  }\n\n  .justify-content-xxl-end {\n    justify-content: flex-end !important;\n  }\n\n  .justify-content-xxl-center {\n    justify-content: center !important;\n  }\n\n  .justify-content-xxl-between {\n    justify-content: space-between !important;\n  }\n\n  .justify-content-xxl-around {\n    justify-content: space-around !important;\n  }\n\n  .justify-content-xxl-evenly {\n    justify-content: space-evenly !important;\n  }\n\n  .align-items-xxl-start {\n    align-items: flex-start !important;\n  }\n\n  .align-items-xxl-end {\n    align-items: flex-end !important;\n  }\n\n  .align-items-xxl-center {\n    align-items: center !important;\n  }\n\n  .align-items-xxl-baseline {\n    align-items: baseline !important;\n  }\n\n  .align-items-xxl-stretch {\n    align-items: stretch !important;\n  }\n\n  .align-content-xxl-start {\n    align-content: flex-start !important;\n  }\n\n  .align-content-xxl-end {\n    align-content: flex-end !important;\n  }\n\n  .align-content-xxl-center {\n    align-content: center !important;\n  }\n\n  .align-content-xxl-between {\n    align-content: space-between !important;\n  }\n\n  .align-content-xxl-around {\n    align-content: space-around !important;\n  }\n\n  .align-content-xxl-stretch {\n    align-content: stretch !important;\n  }\n\n  .align-self-xxl-auto {\n    align-self: auto !important;\n  }\n\n  .align-self-xxl-start {\n    align-self: flex-start !important;\n  }\n\n  .align-self-xxl-end {\n    align-self: flex-end !important;\n  }\n\n  .align-self-xxl-center {\n    align-self: center !important;\n  }\n\n  .align-self-xxl-baseline {\n    align-self: baseline !important;\n  }\n\n  .align-self-xxl-stretch {\n    align-self: stretch !important;\n  }\n\n  .order-xxl-first {\n    order: -1 !important;\n  }\n\n  .order-xxl-0 {\n    order: 0 !important;\n  }\n\n  .order-xxl-1 {\n    order: 1 !important;\n  }\n\n  .order-xxl-2 {\n    order: 2 !important;\n  }\n\n  .order-xxl-3 {\n    order: 3 !important;\n  }\n\n  .order-xxl-4 {\n    order: 4 !important;\n  }\n\n  .order-xxl-5 {\n    order: 5 !important;\n  }\n\n  .order-xxl-last {\n    order: 6 !important;\n  }\n\n  .m-xxl-0 {\n    margin: 0 !important;\n  }\n\n  .m-xxl-1 {\n    margin: 0.25rem !important;\n  }\n\n  .m-xxl-2 {\n    margin: 0.5rem !important;\n  }\n\n  .m-xxl-3 {\n    margin: 1rem !important;\n  }\n\n  .m-xxl-4 {\n    margin: 1.5rem !important;\n  }\n\n  .m-xxl-5 {\n    margin: 3rem !important;\n  }\n\n  .m-xxl-auto {\n    margin: auto !important;\n  }\n\n  .mx-xxl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n\n  .mx-xxl-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n\n  .mx-xxl-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n\n  .mx-xxl-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n\n  .mx-xxl-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n\n  .mx-xxl-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n\n  .mx-xxl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n\n  .my-xxl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n\n  .my-xxl-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n\n  .my-xxl-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .my-xxl-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .my-xxl-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .my-xxl-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n\n  .my-xxl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n\n  .mt-xxl-0 {\n    margin-top: 0 !important;\n  }\n\n  .mt-xxl-1 {\n    margin-top: 0.25rem !important;\n  }\n\n  .mt-xxl-2 {\n    margin-top: 0.5rem !important;\n  }\n\n  .mt-xxl-3 {\n    margin-top: 1rem !important;\n  }\n\n  .mt-xxl-4 {\n    margin-top: 1.5rem !important;\n  }\n\n  .mt-xxl-5 {\n    margin-top: 3rem !important;\n  }\n\n  .mt-xxl-auto {\n    margin-top: auto !important;\n  }\n\n  .me-xxl-0 {\n    margin-right: 0 !important;\n  }\n\n  .me-xxl-1 {\n    margin-right: 0.25rem !important;\n  }\n\n  .me-xxl-2 {\n    margin-right: 0.5rem !important;\n  }\n\n  .me-xxl-3 {\n    margin-right: 1rem !important;\n  }\n\n  .me-xxl-4 {\n    margin-right: 1.5rem !important;\n  }\n\n  .me-xxl-5 {\n    margin-right: 3rem !important;\n  }\n\n  .me-xxl-auto {\n    margin-right: auto !important;\n  }\n\n  .mb-xxl-0 {\n    margin-bottom: 0 !important;\n  }\n\n  .mb-xxl-1 {\n    margin-bottom: 0.25rem !important;\n  }\n\n  .mb-xxl-2 {\n    margin-bottom: 0.5rem !important;\n  }\n\n  .mb-xxl-3 {\n    margin-bottom: 1rem !important;\n  }\n\n  .mb-xxl-4 {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .mb-xxl-5 {\n    margin-bottom: 3rem !important;\n  }\n\n  .mb-xxl-auto {\n    margin-bottom: auto !important;\n  }\n\n  .ms-xxl-0 {\n    margin-left: 0 !important;\n  }\n\n  .ms-xxl-1 {\n    margin-left: 0.25rem !important;\n  }\n\n  .ms-xxl-2 {\n    margin-left: 0.5rem !important;\n  }\n\n  .ms-xxl-3 {\n    margin-left: 1rem !important;\n  }\n\n  .ms-xxl-4 {\n    margin-left: 1.5rem !important;\n  }\n\n  .ms-xxl-5 {\n    margin-left: 3rem !important;\n  }\n\n  .ms-xxl-auto {\n    margin-left: auto !important;\n  }\n\n  .p-xxl-0 {\n    padding: 0 !important;\n  }\n\n  .p-xxl-1 {\n    padding: 0.25rem !important;\n  }\n\n  .p-xxl-2 {\n    padding: 0.5rem !important;\n  }\n\n  .p-xxl-3 {\n    padding: 1rem !important;\n  }\n\n  .p-xxl-4 {\n    padding: 1.5rem !important;\n  }\n\n  .p-xxl-5 {\n    padding: 3rem !important;\n  }\n\n  .px-xxl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n\n  .px-xxl-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n\n  .px-xxl-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n\n  .px-xxl-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n\n  .px-xxl-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n\n  .px-xxl-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n\n  .py-xxl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n\n  .py-xxl-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n\n  .py-xxl-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n\n  .py-xxl-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n\n  .py-xxl-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n\n  .py-xxl-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n\n  .pt-xxl-0 {\n    padding-top: 0 !important;\n  }\n\n  .pt-xxl-1 {\n    padding-top: 0.25rem !important;\n  }\n\n  .pt-xxl-2 {\n    padding-top: 0.5rem !important;\n  }\n\n  .pt-xxl-3 {\n    padding-top: 1rem !important;\n  }\n\n  .pt-xxl-4 {\n    padding-top: 1.5rem !important;\n  }\n\n  .pt-xxl-5 {\n    padding-top: 3rem !important;\n  }\n\n  .pe-xxl-0 {\n    padding-right: 0 !important;\n  }\n\n  .pe-xxl-1 {\n    padding-right: 0.25rem !important;\n  }\n\n  .pe-xxl-2 {\n    padding-right: 0.5rem !important;\n  }\n\n  .pe-xxl-3 {\n    padding-right: 1rem !important;\n  }\n\n  .pe-xxl-4 {\n    padding-right: 1.5rem !important;\n  }\n\n  .pe-xxl-5 {\n    padding-right: 3rem !important;\n  }\n\n  .pb-xxl-0 {\n    padding-bottom: 0 !important;\n  }\n\n  .pb-xxl-1 {\n    padding-bottom: 0.25rem !important;\n  }\n\n  .pb-xxl-2 {\n    padding-bottom: 0.5rem !important;\n  }\n\n  .pb-xxl-3 {\n    padding-bottom: 1rem !important;\n  }\n\n  .pb-xxl-4 {\n    padding-bottom: 1.5rem !important;\n  }\n\n  .pb-xxl-5 {\n    padding-bottom: 3rem !important;\n  }\n\n  .ps-xxl-0 {\n    padding-left: 0 !important;\n  }\n\n  .ps-xxl-1 {\n    padding-left: 0.25rem !important;\n  }\n\n  .ps-xxl-2 {\n    padding-left: 0.5rem !important;\n  }\n\n  .ps-xxl-3 {\n    padding-left: 1rem !important;\n  }\n\n  .ps-xxl-4 {\n    padding-left: 1.5rem !important;\n  }\n\n  .ps-xxl-5 {\n    padding-left: 3rem !important;\n  }\n\n  .text-xxl-start {\n    text-align: left !important;\n  }\n\n  .text-xxl-end {\n    text-align: right !important;\n  }\n\n  .text-xxl-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 1200px) {\n  .fs-1 {\n    font-size: 2.5rem !important;\n  }\n\n  .fs-2 {\n    font-size: 2rem !important;\n  }\n\n  .fs-3 {\n    font-size: 1.75rem !important;\n  }\n\n  .fs-4 {\n    font-size: 1.5rem !important;\n  }\n}\n@media print {\n  .d-print-inline {\n    display: inline !important;\n  }\n\n  .d-print-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-print-block {\n    display: block !important;\n  }\n\n  .d-print-grid {\n    display: grid !important;\n  }\n\n  .d-print-table {\n    display: table !important;\n  }\n\n  .d-print-table-row {\n    display: table-row !important;\n  }\n\n  .d-print-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-print-flex {\n    display: flex !important;\n  }\n\n  .d-print-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-print-none {\n    display: none !important;\n  }\n}\n\n/*# sourceMappingURL=bootstrap-utilities.css.map */\n", "@each $color, $value in $theme-colors {\n  .link-#{$color} {\n    color: $value;\n\n    @if $link-shade-percentage != 0 {\n      &:hover,\n      &:focus {\n        color: if(color-contrast($value) == $color-contrast-light, shade-color($value, $link-shade-percentage), tint-color($value, $link-shade-percentage));\n      }\n    }\n  }\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900\n) !default;\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// scss-docs-start color-variables\n$blue:    #0d6efd !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #d63384 !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #198754 !default;\n$teal:    #20c997 !default;\n$cyan:    #0dcaf0 !default;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n) !default;\n// scss-docs-end colors-map\n\n// scss-docs-start theme-color-variables\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-900 !default;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n) !default;\n// scss-docs-end theme-colors-map\n\n// scss-docs-start theme-colors-rgb\n$theme-colors-rgb: map-loop($theme-colors, to-rgb, \"$value\") !default;\n// scss-docs-end theme-colors-rgb\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio:   4.5 !default;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark:      $black !default;\n$color-contrast-light:     $white !default;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%) !default;\n$blue-200: tint-color($blue, 60%) !default;\n$blue-300: tint-color($blue, 40%) !default;\n$blue-400: tint-color($blue, 20%) !default;\n$blue-500: $blue !default;\n$blue-600: shade-color($blue, 20%) !default;\n$blue-700: shade-color($blue, 40%) !default;\n$blue-800: shade-color($blue, 60%) !default;\n$blue-900: shade-color($blue, 80%) !default;\n\n$indigo-100: tint-color($indigo, 80%) !default;\n$indigo-200: tint-color($indigo, 60%) !default;\n$indigo-300: tint-color($indigo, 40%) !default;\n$indigo-400: tint-color($indigo, 20%) !default;\n$indigo-500: $indigo !default;\n$indigo-600: shade-color($indigo, 20%) !default;\n$indigo-700: shade-color($indigo, 40%) !default;\n$indigo-800: shade-color($indigo, 60%) !default;\n$indigo-900: shade-color($indigo, 80%) !default;\n\n$purple-100: tint-color($purple, 80%) !default;\n$purple-200: tint-color($purple, 60%) !default;\n$purple-300: tint-color($purple, 40%) !default;\n$purple-400: tint-color($purple, 20%) !default;\n$purple-500: $purple !default;\n$purple-600: shade-color($purple, 20%) !default;\n$purple-700: shade-color($purple, 40%) !default;\n$purple-800: shade-color($purple, 60%) !default;\n$purple-900: shade-color($purple, 80%) !default;\n\n$pink-100: tint-color($pink, 80%) !default;\n$pink-200: tint-color($pink, 60%) !default;\n$pink-300: tint-color($pink, 40%) !default;\n$pink-400: tint-color($pink, 20%) !default;\n$pink-500: $pink !default;\n$pink-600: shade-color($pink, 20%) !default;\n$pink-700: shade-color($pink, 40%) !default;\n$pink-800: shade-color($pink, 60%) !default;\n$pink-900: shade-color($pink, 80%) !default;\n\n$red-100: tint-color($red, 80%) !default;\n$red-200: tint-color($red, 60%) !default;\n$red-300: tint-color($red, 40%) !default;\n$red-400: tint-color($red, 20%) !default;\n$red-500: $red !default;\n$red-600: shade-color($red, 20%) !default;\n$red-700: shade-color($red, 40%) !default;\n$red-800: shade-color($red, 60%) !default;\n$red-900: shade-color($red, 80%) !default;\n\n$orange-100: tint-color($orange, 80%) !default;\n$orange-200: tint-color($orange, 60%) !default;\n$orange-300: tint-color($orange, 40%) !default;\n$orange-400: tint-color($orange, 20%) !default;\n$orange-500: $orange !default;\n$orange-600: shade-color($orange, 20%) !default;\n$orange-700: shade-color($orange, 40%) !default;\n$orange-800: shade-color($orange, 60%) !default;\n$orange-900: shade-color($orange, 80%) !default;\n\n$yellow-100: tint-color($yellow, 80%) !default;\n$yellow-200: tint-color($yellow, 60%) !default;\n$yellow-300: tint-color($yellow, 40%) !default;\n$yellow-400: tint-color($yellow, 20%) !default;\n$yellow-500: $yellow !default;\n$yellow-600: shade-color($yellow, 20%) !default;\n$yellow-700: shade-color($yellow, 40%) !default;\n$yellow-800: shade-color($yellow, 60%) !default;\n$yellow-900: shade-color($yellow, 80%) !default;\n\n$green-100: tint-color($green, 80%) !default;\n$green-200: tint-color($green, 60%) !default;\n$green-300: tint-color($green, 40%) !default;\n$green-400: tint-color($green, 20%) !default;\n$green-500: $green !default;\n$green-600: shade-color($green, 20%) !default;\n$green-700: shade-color($green, 40%) !default;\n$green-800: shade-color($green, 60%) !default;\n$green-900: shade-color($green, 80%) !default;\n\n$teal-100: tint-color($teal, 80%) !default;\n$teal-200: tint-color($teal, 60%) !default;\n$teal-300: tint-color($teal, 40%) !default;\n$teal-400: tint-color($teal, 20%) !default;\n$teal-500: $teal !default;\n$teal-600: shade-color($teal, 20%) !default;\n$teal-700: shade-color($teal, 40%) !default;\n$teal-800: shade-color($teal, 60%) !default;\n$teal-900: shade-color($teal, 80%) !default;\n\n$cyan-100: tint-color($cyan, 80%) !default;\n$cyan-200: tint-color($cyan, 60%) !default;\n$cyan-300: tint-color($cyan, 40%) !default;\n$cyan-400: tint-color($cyan, 20%) !default;\n$cyan-500: $cyan !default;\n$cyan-600: shade-color($cyan, 20%) !default;\n$cyan-700: shade-color($cyan, 40%) !default;\n$cyan-800: shade-color($cyan, 60%) !default;\n$cyan-900: shade-color($cyan, 80%) !default;\n\n$blues: (\n  \"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900\n) !default;\n\n$indigos: (\n  \"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900\n) !default;\n\n$purples: (\n  \"purple-100\": $purple-200,\n  \"purple-200\": $purple-100,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900\n) !default;\n\n$pinks: (\n  \"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900\n) !default;\n\n$reds: (\n  \"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900\n) !default;\n\n$oranges: (\n  \"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900\n) !default;\n\n$yellows: (\n  \"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900\n) !default;\n\n$greens: (\n  \"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900\n) !default;\n\n$teals: (\n  \"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900\n) !default;\n\n$cyans: (\n  \"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900\n) !default;\n// fusv-enable\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                true !default;\n$enable-rounded:              true !default;\n$enable-shadows:              false !default;\n$enable-gradients:            false !default;\n$enable-transitions:          true !default;\n$enable-reduced-motion:       true !default;\n$enable-smooth-scroll:        true !default;\n$enable-grid-classes:         true !default;\n$enable-cssgrid:              false !default;\n$enable-button-pointers:      true !default;\n$enable-rfs:                  true !default;\n$enable-validation-icons:     true !default;\n$enable-negative-margins:     false !default;\n$enable-deprecation-messages: true !default;\n$enable-important-utilities:  true !default;\n\n// Prefix for :root CSS variables\n\n$variable-prefix:             bs- !default;\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1rem !default;\n$spacers: (\n  0: 0,\n  1: $spacer * .25,\n  2: $spacer * .5,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n) !default;\n\n$negative-spacers: if($enable-negative-margins, negativify-map($spacers), null) !default;\n// scss-docs-end spacer-variables-maps\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n) !default;\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n$body-text-align:           null !default;\n\n// Utilities maps\n//\n// Extends the default `$theme-colors` maps to help create our utilities.\n\n// scss-docs-start utilities-colors\n$utilities-colors: map-merge(\n  $theme-colors-rgb,\n  (\n    \"black\": to-rgb($black),\n    \"white\": to-rgb($white),\n    \"body\":  to-rgb($body-color)\n  )\n) !default;\n// scss-docs-end utilities-colors\n\n// scss-docs-start utilities-text-colors\n$utilities-text-colors: map-loop($utilities-colors, rgba-css-var, \"$key\", \"text\") !default;\n// scss-docs-end utilities-text-colors\n\n// scss-docs-start utilities-bg-colors\n$utilities-bg-colors: map-loop($utilities-colors, rgba-css-var, \"$key\", \"bg\") !default;\n// scss-docs-end utilities-bg-colors\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              $primary !default;\n$link-decoration:                         underline !default;\n$link-shade-percentage:                   20% !default;\n$link-hover-color:                        shift-color($link-color, $link-shade-percentage) !default;\n$link-hover-decoration:                   null !default;\n\n$stretched-link-pseudo-element:           after !default;\n$stretched-link-z-index:                  1 !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px\n) !default;\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px\n) !default;\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           1.5rem !default;\n$grid-row-columns:            6 !default;\n\n$gutters: $spacers !default;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width * .5 !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width:                1px !default;\n$border-widths: (\n  1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px\n) !default;\n\n$border-color:                $gray-300 !default;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius:               .25rem !default;\n$border-radius-sm:            .2rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-pill:          50rem !default;\n// scss-docs-end border-radius-variables\n\n// scss-docs-start box-shadow-variables\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\n$box-shadow-inset:            inset 0 1px 2px rgba($black, .075) !default;\n// scss-docs-end box-shadow-variables\n\n$component-active-color:      $white !default;\n$component-active-bg:         $primary !default;\n\n// scss-docs-start caret-variables\n$caret-width:                 .3em !default;\n$caret-vertical-align:        $caret-width * .85 !default;\n$caret-spacing:               $caret-width * .85 !default;\n// scss-docs-end caret-variables\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n// scss-docs-start collapse-transition\n$transition-collapse:         height .35s ease !default;\n$transition-collapse-width:   width .35s ease !default;\n// scss-docs-end collapse-transition\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%)\n) !default;\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", \"Liberation Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n// stylelint-enable value-keyword-case\n$font-family-base:            var(--#{$variable-prefix}font-sans-serif) !default;\n$font-family-code:            var(--#{$variable-prefix}font-monospace) !default;\n\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins\n// $font-size-base affects the font size of the body text\n$font-size-root:              null !default;\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-sm:                $font-size-base * .875 !default;\n$font-size-lg:                $font-size-base * 1.25 !default;\n\n$font-weight-lighter:         lighter !default;\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          bolder !default;\n\n$font-weight-base:            $font-weight-normal !default;\n\n$line-height-base:            1.5 !default;\n$line-height-sm:              1.25 !default;\n$line-height-lg:              2 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n) !default;\n// scss-docs-end font-sizes\n\n// scss-docs-start headings-variables\n$headings-margin-bottom:      $spacer * .5 !default;\n$headings-font-family:        null !default;\n$headings-font-style:         null !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              null !default;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display-font-sizes: (\n  1: 5rem,\n  2: 4.5rem,\n  3: 4rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem\n) !default;\n\n$display-font-weight: 300 !default;\n$display-line-height: $headings-line-height !default;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size:              $font-size-base * 1.25 !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             .875em !default;\n\n$sub-sup-font-size:           .75em !default;\n\n$text-muted:                  $gray-600 !default;\n\n$initialism-font-size:        $small-font-size !default;\n\n$blockquote-margin-y:         $spacer !default;\n$blockquote-font-size:        $font-size-base * 1.25 !default;\n$blockquote-footer-color:     $gray-600 !default;\n$blockquote-footer-font-size: $small-font-size !default;\n\n$hr-margin-y:                 $spacer !default;\n$hr-color:                    inherit !default;\n$hr-height:                   $border-width !default;\n$hr-opacity:                  .25 !default;\n\n$legend-margin-bottom:        .5rem !default;\n$legend-font-size:            1.5rem !default;\n$legend-font-weight:          null !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n// scss-docs-end type-variables\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:        .5rem !default;\n$table-cell-padding-x:        .5rem !default;\n$table-cell-padding-y-sm:     .25rem !default;\n$table-cell-padding-x-sm:     .25rem !default;\n\n$table-cell-vertical-align:   top !default;\n\n$table-color:                 $body-color !default;\n$table-bg:                    transparent !default;\n$table-accent-bg:             transparent !default;\n\n$table-th-font-weight:        null !default;\n\n$table-striped-color:         $table-color !default;\n$table-striped-bg-factor:     .05 !default;\n$table-striped-bg:            rgba($black, $table-striped-bg-factor) !default;\n\n$table-active-color:          $table-color !default;\n$table-active-bg-factor:      .1 !default;\n$table-active-bg:             rgba($black, $table-active-bg-factor) !default;\n\n$table-hover-color:           $table-color !default;\n$table-hover-bg-factor:       .075 !default;\n$table-hover-bg:              rgba($black, $table-hover-bg-factor) !default;\n\n$table-border-factor:         .1 !default;\n$table-border-width:          $border-width !default;\n$table-border-color:          $border-color !default;\n\n$table-striped-order:         odd !default;\n\n$table-group-separator-color: currentColor !default;\n\n$table-caption-color:         $text-muted !default;\n\n$table-bg-scale:              -80% !default;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\n  \"primary\":    shift-color($primary, $table-bg-scale),\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\n  \"success\":    shift-color($success, $table-bg-scale),\n  \"info\":       shift-color($info, $table-bg-scale),\n  \"warning\":    shift-color($warning, $table-bg-scale),\n  \"danger\":     shift-color($danger, $table-bg-scale),\n  \"light\":      $light,\n  \"dark\":       $dark,\n) !default;\n// scss-docs-end table-loop\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-font-family:       null !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:         .25rem !default;\n$input-btn-focus-color-opacity: .25 !default;\n$input-btn-focus-color:         rgba($component-active-bg, $input-btn-focus-color-opacity) !default;\n$input-btn-focus-blur:          0 !default;\n$input-btn-focus-box-shadow:    0 0 $input-btn-focus-blur $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n// scss-docs-end input-btn-variables\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-color:              $link-color !default;\n$btn-link-hover-color:        $link-hover-color !default;\n$btn-link-disabled-color:     $gray-600 !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$btn-hover-bg-shade-amount:       15% !default;\n$btn-hover-bg-tint-amount:        15% !default;\n$btn-hover-border-shade-amount:   20% !default;\n$btn-hover-border-tint-amount:    10% !default;\n$btn-active-bg-shade-amount:      20% !default;\n$btn-active-bg-tint-amount:       20% !default;\n$btn-active-border-shade-amount:  25% !default;\n$btn-active-border-tint-amount:   10% !default;\n// scss-docs-end btn-variables\n\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top:                  .25rem !default;\n$form-text-font-size:                   $small-font-size !default;\n$form-text-font-style:                  null !default;\n$form-text-font-weight:                 null !default;\n$form-text-color:                       $text-muted !default;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom:              .5rem !default;\n$form-label-font-size:                  null !default;\n$form-label-font-style:                 null !default;\n$form-label-font-weight:                null !default;\n$form-label-color:                      null !default;\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-base !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n\n$input-bg:                              $body-bg !default;\n$input-disabled-bg:                     $gray-200 !default;\n$input-disabled-border-color:           null !default;\n\n$input-color:                           $body-color !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      $box-shadow-inset !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              tint-color($component-active-bg, 50%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color:               $gray-600 !default;\n$input-plaintext-color:                 $body-color !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y * .5) !default;\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-color-width:                      3rem !default;\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width:                  1em !default;\n$form-check-min-height:                   $font-size-base * $line-height-base !default;\n$form-check-padding-start:                $form-check-input-width + .5em !default;\n$form-check-margin-bottom:                .125rem !default;\n$form-check-label-color:                  null !default;\n$form-check-label-cursor:                 null !default;\n$form-check-transition:                   null !default;\n\n$form-check-input-active-filter:          brightness(90%) !default;\n\n$form-check-input-bg:                     $input-bg !default;\n$form-check-input-border:                 1px solid rgba($black, .25) !default;\n$form-check-input-border-radius:          .25em !default;\n$form-check-radio-border-radius:          50% !default;\n$form-check-input-focus-border:           $input-focus-border-color !default;\n$form-check-input-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n\n$form-check-input-checked-color:          $component-active-color !default;\n$form-check-input-checked-bg-color:       $component-active-bg !default;\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/></svg>\") !default;\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color:          $component-active-color !default;\n$form-check-input-indeterminate-bg-color:       $component-active-bg !default;\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\n\n$form-check-input-disabled-opacity:        .5 !default;\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity !default;\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity !default;\n\n$form-check-inline-margin-end:    1rem !default;\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n$form-switch-color:               rgba(0, 0, 0, .25) !default;\n$form-switch-width:               2em !default;\n$form-switch-padding-start:       $form-switch-width + .5em !default;\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\n$form-switch-border-radius:       $form-switch-width !default;\n$form-switch-transition:          background-position .15s ease-in-out !default;\n\n$form-switch-focus-color:         $input-focus-border-color !default;\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\n\n$form-switch-checked-color:       $component-active-color !default;\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\n$form-switch-checked-bg-position: right center !default;\n// scss-docs-end form-switch-variables\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y:           $input-padding-y !default;\n$input-group-addon-padding-x:           $input-padding-x !default;\n$input-group-addon-font-weight:         $input-font-weight !default;\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y:             $input-padding-y !default;\n$form-select-padding-x:             $input-padding-x !default;\n$form-select-font-family:           $input-font-family !default;\n$form-select-font-size:             $input-font-size !default;\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\n$form-select-font-weight:           $input-font-weight !default;\n$form-select-line-height:           $input-line-height !default;\n$form-select-color:                 $input-color !default;\n$form-select-bg:                    $input-bg !default;\n$form-select-disabled-color:        null !default;\n$form-select-disabled-bg:           $gray-200 !default;\n$form-select-disabled-border-color: $input-disabled-border-color !default;\n$form-select-bg-position:           right $form-select-padding-x center !default;\n$form-select-bg-size:               16px 12px !default; // In pixels because image dimensions\n$form-select-indicator-color:       $gray-800 !default;\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/></svg>\") !default;\n\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\n\n$form-select-border-width:        $input-border-width !default;\n$form-select-border-color:        $input-border-color !default;\n$form-select-border-radius:       $border-radius !default;\n$form-select-box-shadow:          $box-shadow-inset !default;\n\n$form-select-focus-border-color:  $input-focus-border-color !default;\n$form-select-focus-width:         $input-focus-width !default;\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color !default;\n\n$form-select-padding-y-sm:        $input-padding-y-sm !default;\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\n$form-select-font-size-sm:        $input-font-size-sm !default;\n\n$form-select-padding-y-lg:        $input-padding-y-lg !default;\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\n$form-select-font-size-lg:        $input-font-size-lg !default;\n\n$form-select-transition:          $input-transition !default;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width:          100% !default;\n$form-range-track-height:         .5rem !default;\n$form-range-track-cursor:         pointer !default;\n$form-range-track-bg:             $gray-300 !default;\n$form-range-track-border-radius:  1rem !default;\n$form-range-track-box-shadow:     $box-shadow-inset !default;\n\n$form-range-thumb-width:                   1rem !default;\n$form-range-thumb-height:                  $form-range-thumb-width !default;\n$form-range-thumb-bg:                      $component-active-bg !default;\n$form-range-thumb-border:                  0 !default;\n$form-range-thumb-border-radius:           1rem !default;\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 70%) !default;\n$form-range-thumb-disabled-bg:             $gray-500 !default;\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color:          $input-color !default;\n$form-file-button-bg:             $input-group-addon-bg !default;\n$form-file-button-hover-bg:       shade-color($form-file-button-bg, 5%) !default;\n// scss-docs-end form-file-variables\n\n// scss-docs-start form-floating-variables\n$form-floating-height:            add(3.5rem, $input-height-border) !default;\n$form-floating-line-height:       1.25 !default;\n$form-floating-padding-x:         $input-padding-x !default;\n$form-floating-padding-y:         1rem !default;\n$form-floating-input-padding-t:   1.625rem !default;\n$form-floating-input-padding-b:   .625rem !default;\n$form-floating-label-opacity:     .65 !default;\n$form-floating-label-transform:   scale(.85) translateY(-.5rem) translateX(.15rem) !default;\n$form-floating-transition:        opacity .1s ease-in-out, transform .1s ease-in-out !default;\n// scss-docs-end form-floating-variables\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $form-text-font-size !default;\n$form-feedback-font-style:          $form-text-font-style !default;\n$form-feedback-valid-color:         $success !default;\n$form-feedback-invalid-color:       $danger !default;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\n    \"color\": $form-feedback-valid-color,\n    \"icon\": $form-feedback-icon-valid\n  ),\n  \"invalid\": (\n    \"color\": $form-feedback-invalid-color,\n    \"icon\": $form-feedback-icon-invalid\n  )\n) !default;\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-offcanvas-backdrop:         1040 !default;\n$zindex-offcanvas:                  1045 !default;\n$zindex-modal-backdrop:             1050 !default;\n$zindex-modal:                      1055 !default;\n$zindex-popover:                    1070 !default;\n$zindex-tooltip:                    1080 !default;\n// scss-docs-end zindex-stack\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-font-size:                null !default;\n$nav-link-font-weight:              null !default;\n$nav-link-color:                    $link-color !default;\n$nav-link-hover-color:              $link-hover-color !default;\n$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y:                  $spacer * .5 !default;\n$navbar-padding-x:                  null !default;\n\n$navbar-nav-link-padding-x:         .5rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) * .5 !default;\n$navbar-brand-margin-end:           1rem !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n$navbar-toggler-focus-width:        $btn-focus-width !default;\n$navbar-toggler-transition:         box-shadow .15s ease-in-out !default;\n// scss-docs-end navbar-variables\n\n// scss-docs-start navbar-theme-variables\n$navbar-dark-color:                 rgba($white, .55) !default;\n$navbar-dark-hover-color:           rgba($white, .75) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .55) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n$navbar-light-brand-color:                $navbar-light-active-color !default;\n$navbar-light-brand-hover-color:          $navbar-light-active-color !default;\n$navbar-dark-brand-color:                 $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color !default;\n// scss-docs-end navbar-theme-variables\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-x:                0 !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-color:                    $body-color !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-inner-border-radius:      subtract($dropdown-border-radius, $dropdown-border-width) !default;\n$dropdown-divider-bg:               $dropdown-border-color !default;\n$dropdown-divider-margin-y:         $spacer * .5 !default;\n$dropdown-box-shadow:               $box-shadow !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         shade-color($gray-900, 10%) !default;\n$dropdown-link-hover-bg:            $gray-200 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-500 !default;\n\n$dropdown-item-padding-y:           $spacer * .25 !default;\n$dropdown-item-padding-x:           $spacer !default;\n\n$dropdown-header-color:             $gray-600 !default;\n$dropdown-header-padding:           $dropdown-padding-y $dropdown-item-padding-x !default;\n// scss-docs-end dropdown-variables\n\n// scss-docs-start dropdown-dark-variables\n$dropdown-dark-color:               $gray-300 !default;\n$dropdown-dark-bg:                  $gray-800 !default;\n$dropdown-dark-border-color:        $dropdown-border-color !default;\n$dropdown-dark-divider-bg:          $dropdown-divider-bg !default;\n$dropdown-dark-box-shadow:          null !default;\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\n$dropdown-dark-link-hover-color:    $white !default;\n$dropdown-dark-link-hover-bg:       rgba($white, .15) !default;\n$dropdown-dark-link-active-color:   $dropdown-link-active-color !default;\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg !default;\n$dropdown-dark-link-disabled-color: $gray-500 !default;\n$dropdown-dark-header-color:        $gray-500 !default;\n// scss-docs-end dropdown-dark-variables\n\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y:              .375rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-radius:          $border-radius !default;\n$pagination-margin-start:           -$pagination-border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-color:            $link-hover-color !default;\n$pagination-focus-bg:               $gray-200 !default;\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n$pagination-focus-outline:          0 !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$pagination-border-radius-sm:       $border-radius-sm !default;\n$pagination-border-radius-lg:       $border-radius-lg !default;\n// scss-docs-end pagination-variables\n\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max:           .5 !default;\n$placeholder-opacity-min:           .2 !default;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-y:                     $spacer !default;\n$card-spacer-x:                     $spacer !default;\n$card-title-spacer-y:               $spacer * .5 !default;\n$card-border-width:                 $border-width !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-border-radius:                $border-radius !default;\n$card-box-shadow:                   null !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-padding-y:                $card-spacer-y * .5 !default;\n$card-cap-padding-x:                $card-spacer-x !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-cap-color:                    null !default;\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           $white !default;\n$card-img-overlay-padding:          $spacer !default;\n$card-group-margin:                 $grid-gutter-width * .5 !default;\n// scss-docs-end card-variables\n\n// Accordion\n\n// scss-docs-start accordion-variables\n$accordion-padding-y:                     1rem !default;\n$accordion-padding-x:                     1.25rem !default;\n$accordion-color:                         $body-color !default;\n$accordion-bg:                            $body-bg !default;\n$accordion-border-width:                  $border-width !default;\n$accordion-border-color:                  rgba($black, .125) !default;\n$accordion-border-radius:                 $border-radius !default;\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width) !default;\n\n$accordion-body-padding-y:                $accordion-padding-y !default;\n$accordion-body-padding-x:                $accordion-padding-x !default;\n\n$accordion-button-padding-y:              $accordion-padding-y !default;\n$accordion-button-padding-x:              $accordion-padding-x !default;\n$accordion-button-color:                  $accordion-color !default;\n$accordion-button-bg:                     $accordion-bg !default;\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\n$accordion-button-active-bg:              tint-color($component-active-bg, 90%) !default;\n$accordion-button-active-color:           shade-color($primary, 10%) !default;\n\n$accordion-button-focus-border-color:     $input-focus-border-color !default;\n$accordion-button-focus-box-shadow:       $btn-focus-box-shadow !default;\n\n$accordion-icon-width:                    1.25rem !default;\n$accordion-icon-color:                    $accordion-button-color !default;\n$accordion-icon-active-color:             $accordion-button-active-color !default;\n$accordion-icon-transition:               transform .2s ease-in-out !default;\n$accordion-icon-transform:                rotate(-180deg) !default;\n\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-max-width:                 200px !default;\n$tooltip-color:                     $white !default;\n$tooltip-bg:                        $black !default;\n$tooltip-border-radius:             $border-radius !default;\n$tooltip-opacity:                   .9 !default;\n$tooltip-padding-y:                 $spacer * .25 !default;\n$tooltip-padding-x:                 $spacer * .5 !default;\n$tooltip-margin:                    0 !default;\n\n$tooltip-arrow-width:               .8rem !default;\n$tooltip-arrow-height:              .4rem !default;\n$tooltip-arrow-color:               $tooltip-bg !default;\n// scss-docs-end tooltip-variables\n\n// Form tooltips must come after regular tooltips\n// scss-docs-start tooltip-feedback-variables\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   null !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n// scss-docs-end tooltip-feedback-variables\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width) !default;\n$popover-box-shadow:                $box-shadow !default;\n\n$popover-header-bg:                 shade-color($popover-bg, 6%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          $spacer !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $spacer !default;\n$popover-body-padding-x:            $spacer !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n// scss-docs-end popover-variables\n\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width:                   350px !default;\n$toast-padding-x:                   .75rem !default;\n$toast-padding-y:                   .5rem !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       null !default;\n$toast-background-color:            rgba($white, .85) !default;\n$toast-border-width:                1px !default;\n$toast-border-color:                rgba(0, 0, 0, .1) !default;\n$toast-border-radius:               $border-radius !default;\n$toast-box-shadow:                  $box-shadow !default;\n$toast-spacing:                     $container-padding-x !default;\n\n$toast-header-color:                $gray-600 !default;\n$toast-header-background-color:     rgba($white, .85) !default;\n$toast-header-border-color:         rgba(0, 0, 0, .05) !default;\n// scss-docs-end toast-variables\n\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size:                   .75em !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-color:                       $white !default;\n$badge-padding-y:                   .35em !default;\n$badge-padding-x:                   .65em !default;\n$badge-border-radius:               $border-radius !default;\n// scss-docs-end badge-variables\n\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding:               $spacer !default;\n\n$modal-footer-margin-between:       .5rem !default;\n\n$modal-dialog-margin:               .5rem !default;\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-color:               null !default;\n$modal-content-bg:                  $white !default;\n$modal-content-border-color:        rgba($black, .2) !default;\n$modal-content-border-width:        $border-width !default;\n$modal-content-border-radius:       $border-radius-lg !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs:       $box-shadow-sm !default;\n$modal-content-box-shadow-sm-up:    $box-shadow !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n$modal-header-border-color:         $border-color !default;\n$modal-footer-border-color:         $modal-header-border-color !default;\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-footer-border-width:         $modal-header-border-width !default;\n$modal-header-padding-y:            $modal-inner-padding !default;\n$modal-header-padding-x:            $modal-inner-padding !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-sm:                          300px !default;\n$modal-md:                          500px !default;\n$modal-lg:                          800px !default;\n$modal-xl:                          1140px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n// scss-docs-end modal-variables\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y:               $spacer !default;\n$alert-padding-x:               $spacer !default;\n$alert-margin-bottom:           1rem !default;\n$alert-border-radius:           $border-radius !default;\n$alert-link-font-weight:        $font-weight-bold !default;\n$alert-border-width:            $border-width !default;\n$alert-bg-scale:                -80% !default;\n$alert-border-scale:            -70% !default;\n$alert-color-scale:             40% !default;\n$alert-dismissible-padding-r:   $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height:                   1rem !default;\n$progress-font-size:                $font-size-base * .75 !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               $box-shadow-inset !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   $primary !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n// scss-docs-end progress-variables\n\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-color:                  $gray-900 !default;\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         $spacer * .5 !default;\n$list-group-item-padding-x:         $spacer !default;\n$list-group-item-bg-scale:          -80% !default;\n$list-group-item-color-scale:       40% !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              $box-shadow-sm !default;\n// scss-docs-end thumbnail-variables\n\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size:          $small-font-size !default;\n$figure-caption-color:              $gray-600 !default;\n// scss-docs-end figure-variables\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size:              null !default;\n$breadcrumb-padding-y:              0 !default;\n$breadcrumb-padding-x:              0 !default;\n$breadcrumb-item-padding-x:         .5rem !default;\n$breadcrumb-margin-bottom:          1rem !default;\n$breadcrumb-bg:                     null !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                quote(\"/\") !default;\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\n$breadcrumb-border-radius:          null !default;\n// scss-docs-end breadcrumb-variables\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color:             $white !default;\n$carousel-control-width:             15% !default;\n$carousel-control-opacity:           .5 !default;\n$carousel-control-hover-opacity:     .9 !default;\n$carousel-control-transition:        opacity .15s ease !default;\n\n$carousel-indicator-width:           30px !default;\n$carousel-indicator-height:          3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer:          3px !default;\n$carousel-indicator-opacity:         .5 !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-active-opacity:  1 !default;\n$carousel-indicator-transition:      opacity .6s ease !default;\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             $white !default;\n$carousel-caption-padding-y:         1.25rem !default;\n$carousel-caption-spacer:            1.25rem !default;\n\n$carousel-control-icon-width:        2rem !default;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n$carousel-dark-indicator-active-bg:  $black !default;\n$carousel-dark-caption-color:        $black !default;\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100) !default;\n// scss-docs-end carousel-variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-width:           2rem !default;\n$spinner-height:          $spinner-width !default;\n$spinner-vertical-align:  -.125em !default;\n$spinner-border-width:    .25em !default;\n$spinner-animation-speed: .75s !default;\n\n$spinner-width-sm:        1rem !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width:            1em !default;\n$btn-close-height:           $btn-close-width !default;\n$btn-close-padding-x:        .25em !default;\n$btn-close-padding-y:        $btn-close-padding-x !default;\n$btn-close-color:            $black !default;\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\") !default;\n$btn-close-focus-shadow:     $input-btn-focus-box-shadow !default;\n$btn-close-opacity:          .5 !default;\n$btn-close-hover-opacity:    .75 !default;\n$btn-close-focus-opacity:    1 !default;\n$btn-close-disabled-opacity: .25 !default;\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%) !default;\n// scss-docs-end close-variables\n\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y:               $modal-inner-padding !default;\n$offcanvas-padding-x:               $modal-inner-padding !default;\n$offcanvas-horizontal-width:        400px !default;\n$offcanvas-vertical-height:         30vh !default;\n$offcanvas-transition-duration:     .3s !default;\n$offcanvas-border-color:            $modal-content-border-color !default;\n$offcanvas-border-width:            $modal-content-border-width !default;\n$offcanvas-title-line-height:       $modal-title-line-height !default;\n$offcanvas-bg-color:                $modal-content-bg !default;\n$offcanvas-color:                   $modal-content-color !default;\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs !default;\n$offcanvas-backdrop-bg:             $modal-backdrop-bg !default;\n$offcanvas-backdrop-opacity:        $modal-backdrop-opacity !default;\n// scss-docs-end offcanvas-variables\n\n// Code\n\n$code-font-size:                    $small-font-size !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         null !default;\n", "// Credit: <PERSON> and <PERSON><PERSON><PERSON> CSS.\n\n.ratio {\n  position: relative;\n  width: 100%;\n\n  &::before {\n    display: block;\n    padding-top: var(--#{$variable-prefix}aspect-ratio);\n    content: \"\";\n  }\n\n  > * {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n  }\n}\n\n@each $key, $ratio in $aspect-ratios {\n  .ratio-#{$key} {\n    --#{$variable-prefix}aspect-ratio: #{$ratio};\n  }\n}\n", "// Shorthand\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n// Responsive sticky top\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .sticky#{$infix}-top {\n      position: sticky;\n      top: 0;\n      z-index: $zindex-sticky;\n    }\n  }\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// scss-docs-start stacks\n.hstack {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  align-self: stretch;\n}\n\n.vstack {\n  display: flex;\n  flex: 1 1 auto;\n  flex-direction: column;\n  align-self: stretch;\n}\n// scss-docs-end stacks\n", "//\n// Visually hidden\n//\n\n.visually-hidden,\n.visually-hidden-focusable:not(:focus):not(:focus-within) {\n  @include visually-hidden();\n}\n", "// stylelint-disable declaration-no-important\n\n// Hide content visually while keeping it accessible to assistive technologies\n//\n// See: https://www.a11yproject.com/posts/2013-01-11-how-to-hide-content/\n// See: https://hugogiraudel.com/2016/10/13/css-hide-and-seek/\n\n@mixin visually-hidden() {\n  position: absolute !important;\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important; // Fix for https://github.com/twbs/bootstrap/issues/25686\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n}\n\n// Use to only display content when it's focused, or one of its child elements is focused\n// (i.e. when focus is within the element/container that the class was applied to)\n//\n// Useful for \"Skip to main content\" links; see https://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n\n@mixin visually-hidden-focusable() {\n  &:not(:focus):not(:focus-within) {\n    @include visually-hidden();\n  }\n}\n", "//\n// Stretched link\n//\n\n.stretched-link {\n  &::#{$stretched-link-pseudo-element} {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: $stretched-link-z-index;\n    content: \"\";\n  }\n}\n", "//\n// Text truncation\n//\n\n.text-truncate {\n  @include text-truncate();\n}\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", ".vr {\n  display: inline-block;\n  align-self: stretch;\n  width: 1px;\n  min-height: 1em;\n  background-color: currentColor;\n  opacity: $hr-opacity;\n}\n", "// Utility generator\n// Used to generate utilities & print utilities\n@mixin generate-utility($utility, $infix, $is-rfs-media-query: false) {\n  $values: map-get($utility, values);\n\n  // If the values are a list or string, convert it into a map\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\n    $values: zip($values, $values);\n  }\n\n  @each $key, $value in $values {\n    $properties: map-get($utility, property);\n\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\n    @if type-of($properties) == \"string\" {\n      $properties: append((), $properties);\n    }\n\n    // Use custom class if present\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\n    $property-class: if($property-class == null, \"\", $property-class);\n\n    // State params to generate pseudo-classes\n    $state: if(map-has-key($utility, state), map-get($utility, state), ());\n\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\n\n    // Don't prefix if value key is null (eg. with shadow class)\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\n\n    @if map-get($utility, rfs) {\n      // Inside the media query\n      @if $is-rfs-media-query {\n        $val: rfs-value($value);\n\n        // Do not render anything if fluid and non fluid values are the same\n        $value: if($val == rfs-fluid-value($value), null, $val);\n      }\n      @else {\n        $value: rfs-fluid-value($value);\n      }\n    }\n\n    $is-css-var: map-get($utility, css-var);\n    $is-local-vars: map-get($utility, local-vars);\n    $is-rtl: map-get($utility, rtl);\n\n    @if $value != null {\n      @if $is-rtl == false {\n        /* rtl:begin:remove */\n      }\n\n      @if $is-css-var {\n        .#{$property-class + $infix + $property-class-modifier} {\n          --#{$variable-prefix}#{$property-class}: #{$value};\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            --#{$variable-prefix}#{$property-class}: #{$value};\n          }\n        }\n      } @else {\n        .#{$property-class + $infix + $property-class-modifier} {\n          @each $property in $properties {\n            @if $is-local-vars {\n              @each $local-var, $value in $is-local-vars {\n                --#{$variable-prefix}#{$local-var}: #{$value};\n              }\n            }\n            #{$property}: $value if($enable-important-utilities, !important, null);\n          }\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            @each $property in $properties {\n              #{$property}: $value if($enable-important-utilities, !important, null);\n            }\n          }\n        }\n      }\n\n      @if $is-rtl == false {\n        /* rtl:end:remove */\n      }\n    }\n  }\n}\n", "// Loop over each breakpoint\n@each $breakpoint in map-keys($grid-breakpoints) {\n\n  // Generate media query if needed\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    // Loop over each utility property\n    @each $key, $utility in $utilities {\n      // The utility can be disabled with `false`, thus check if the utility is a map first\n      // Only proceed if responsive media queries are enabled or if it's the base media query\n      @if type-of($utility) == \"map\" and (map-get($utility, responsive) or $infix == \"\") {\n        @include generate-utility($utility, $infix);\n      }\n    }\n  }\n}\n\n// RFS rescaling\n@media (min-width: $rfs-mq-value) {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @if (map-get($grid-breakpoints, $breakpoint) < $rfs-breakpoint) {\n      // Loop over each utility property\n      @each $key, $utility in $utilities {\n        // The utility can be disabled with `false`, thus check if the utility is a map first\n        // Only proceed if responsive media queries are enabled or if it's the base media query\n        @if type-of($utility) == \"map\" and map-get($utility, rfs) and (map-get($utility, responsive) or $infix == \"\") {\n          @include generate-utility($utility, $infix, true);\n        }\n      }\n    }\n  }\n}\n\n\n// Print utilities\n@media print {\n  @each $key, $utility in $utilities {\n    // The utility can be disabled with `false`, thus check if the utility is a map first\n    // Then check if the utility needs print styles\n    @if type-of($utility) == \"map\" and map-get($utility, print) == true {\n      @include generate-utility($utility, \"-print\");\n    }\n  }\n}\n"]}
﻿@model F4_API.Models.KhachHang

@{
    ViewData["Title"] = "Sửa khách hàng";
}

<h2>Sửa khách hàng</h2>

<form asp-action="Edit" method="post">
    <input type="hidden" asp-for="KhachHangId" />

    <div class="mb-3">
        <label asp-for="TenKhachHang" class="form-label"></label>
        <input asp-for="TenKhachHang" class="form-control" />
        <span asp-validation-for="TenKhachHang" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="Email" class="form-label"></label>
        <input asp-for="Email" class="form-control" />
        <span asp-validation-for="Email" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="Sdt" class="form-label"></label>
        <input asp-for="Sdt" class="form-control" />
        <span asp-validation-for="Sdt" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="GioiTinh" class="form-label">Giới tính</label>
        <select asp-for="GioiTinh" class="form-select">
            <option value="true">Nam</option>
            <option value="false">Nữ</option>
        </select>
    </div>

    <div class="mb-3">
        <label asp-for="TrangThai" class="form-label">Trạng thái</label>
        <select asp-for="TrangThai" class="form-select">
            <option value="true">Hoạt động</option>
            <option value="false">Khóa</option>
        </select>
    </div>

    <button type="submit" class="btn btn-success">Lưu</button>
    <a asp-action="Index" class="btn btn-secondary">Hủy</a>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}

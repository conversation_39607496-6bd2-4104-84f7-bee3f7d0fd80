﻿@{
    Layout = "~/Views/Shared/_Layout.cshtml";
    ViewData["Title"] = "Đăng ký nhân viên";
}

<div class="container mt-5">
    <div class="card shadow-lg border-0">
        <div class="card-header bg-primary text-white text-center">
            <h4><i class="fas fa-user-plus me-2"></i> Đăng Ký Nhân Viên Mới</h4>
        </div>

        <div class="card-body p-4">
            @if (ViewBag.loii != null)
            {
                <div class="alert alert-danger text-center">@ViewBag.loii</div>
            }

            <form asp-action="DangKyNhanVien" method="post">
                @Html.AntiForgeryToken()

                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label fw-semibold">Họ và tên <span class="text-danger">*</span></label>
                        <input name="HoVaTen" class="form-control" placeholder="Nguyễn Văn A" required />
                    </div>

                    <div class="col-md-6">
                        <label class="form-label fw-semibold">Email <span class="text-danger">*</span></label>
                        <input type="email" name="Email" class="form-control" placeholder="<EMAIL>" required />
                    </div>

                    <div class="col-md-6">
                        <label class="form-label fw-semibold">Số điện thoại <span class="text-danger">*</span></label>
                        <input name="SoDienThoai" class="form-control" placeholder="098xxxxxxx" required />
                    </div>

                    <div class="col-md-6">
                        <label class="form-label fw-semibold">Địa chỉ</label>
                        <input name="DiaChi" class="form-control" placeholder="Số nhà, phường, quận..." />
                    </div>

                    <div class="col-md-4">
                        <label class="form-label fw-semibold">Tên tài khoản <span class="text-danger">*</span></label>
                        <input name="Tk" class="form-control" required />
                    </div>

                    <div class="col-md-4">
                        <label class="form-label fw-semibold">Mật khẩu <span class="text-danger">*</span></label>
                        <input type="password" name="MK1" class="form-control" required />
                    </div>

                    <div class="col-md-4">
                        <label class="form-label fw-semibold">Xác nhận mật khẩu <span class="text-danger">*</span></label>
                        <input type="password" name="MK2" class="form-control" required />
                    </div>

                    <div class="col-md-6">
                        <label class="form-label fw-semibold">Giới tính</label><br />
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input" name="GioiTinh" value="Nam" checked />
                            <label class="form-check-label">Nam</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input" name="GioiTinh" value="Nữ" />
                            <label class="form-check-label">Nữ</label>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label fw-semibold">Ngày sinh</label>
                        <input type="date" name="NgaySinh" class="form-control" required />
                    </div>
                </div>

                <div class="text-end mt-4">
                    <button type="submit" class="btn btn-primary fw-bold">
                        <i class="fas fa-save me-1"></i> Lưu thông tin
                    </button>
                    <a href="@Url.Action("Index", "NhanVien")" class="btn btn-secondary ms-2">Quay lại</a>
                </div>
            </form>
        </div>
    </div>
</div>

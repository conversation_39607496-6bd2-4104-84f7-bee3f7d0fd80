﻿using F4_API.DATA;
using F4_API.Models;
using F4_API.Repository.IRepository;
using Microsoft.EntityFrameworkCore;

namespace F4_API.Repository
{
    public class NhanVienrepository : INhanVienRepository
    {
        private readonly AppDbContext _db;
        public NhanVienrepository(AppDbContext db)
        {
            _db = db;
        }
        public async Task CreateNhanVien(NhanVien nhanVien)
        {
            try
            {

                if (nhanVien.TaiKhoanId == Guid.Empty)
                {
                    nhanVien.TaiKhoanId = null;
                }


                if (nhanVien.ChucVuId == null || nhanVien.ChucVuId == Guid.Empty)
                {
                    nhanVien.ChucVuId = Guid.Parse("*************-2222-2222-************");
                }


                nhanVien.NgayCapNhatCuoiCung = DateTime.Now;

                _db.NhanViens.Add(nhanVien);
                await _db.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi tạo nhân viên: {ex.Message}");
            }
        }

        public async Task DeleteNhanVienAsync(Guid NhanVienId)
        {
            try
            {
                var FindIdNhanVien = await GetByIdNhanVienAsync(NhanVienId);
                if (FindIdNhanVien != null)
                {
                    _db.Remove<NhanVien>(FindIdNhanVien);
                    await _db.SaveChangesAsync();
                }
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<List<NhanVien>> GetAllNhanVienAsync()
        {
            return await _db.NhanViens
                            .Include(nv => nv.ChucVu)
                            .ToListAsync();
        }

        public async Task<NhanVien> GetByIdNhanVienAsync(Guid NhanVienId)
        {
            try
            {
                return await _db.NhanViens.FindAsync(NhanVienId);
            }
            catch (Exception)
            {
                throw;
            }
        }
        public async Task<NhanVien> GetIdNhanVienTaiKhoan(Guid tk)
        {
            return await _db.NhanViens.FirstOrDefaultAsync(TK => TK.TaiKhoanId == tk);
        }

        public async Task UpdateNhanVienAsync(NhanVien nhanVien)
        {
            try
            {
                var existing = await _db.NhanViens.FindAsync(nhanVien.NhanVienId);
                if (existing == null)
                    throw new Exception("Không tìm thấy nhân viên cần cập nhật.");

                // Chỉ cập nhật các trường thay đổi
                _db.Entry(existing).CurrentValues.SetValues(nhanVien);

                await _db.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("Lỗi cập nhật nhân viên: " + ex.Message);
            }
        }
    }
}
